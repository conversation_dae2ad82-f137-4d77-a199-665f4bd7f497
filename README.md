# Op Purchase Ai

机会采买的后端AI服务

##

## 开发环境

### Python版本

使用的[Paddle镜像](https://www.paddlepaddle.org.cn/documentation/docs/zh/develop/install/docker/linux-docker.html)是3.10版本

当前使用镜像是 `paddlepaddle/paddle:2.6.1-gpu-cuda11.2-cudnn8.2-trt8.0`

### 运行依赖

```
pip install -r requirements.txt -i http://pypi.vip.vip.com/simple/ --trusted-host pypi.vip.vip.com
```

### 环境变量

|KEY|说明|默认值|
|---|---|---|
|PYTHONPATH|用于配置python代码路径,jetbrains上默认是项目根目录|-|
|PROFILE|运行环境|production|
|OCR_DET_MODEL|OCR文本检测模型|ch_PP-OCRv4_server_det|
|OCR_REC_MODEL|OCR文本识别模型|ch_PP-OCRv4_server_rec|
|UVICORN_PORT|服务启动端口 ai-noah上要求是80|80|

开发环境建议 复制 `.env.sample` 重命名至 `.env`，IDE中选择该文件作为环境变量

### Host

本地运行 需要以下host

```
# 回归环境的vfs
************** vis-fs.api.vip.com
```


## 部署

### 流水线

流水线负责将代码打成压缩包上传，而上传有[敏感文件检测](https://wiki.corp.vipshop.com/pages/viewpage.action?pageId=463965406)

因此流水线中特定选择了具体文件进行打包，如迭代版本增加新的文件，可能需要修改流水线命令。