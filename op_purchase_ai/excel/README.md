# Excel to Markdown 转换器

这个模块提供了将 Excel 文件转换为 Markdown 表格格式的功能，支持合并单元格和日期格式化。

## 功能特性

- ✅ 读取 Excel 文件的第一个工作表
- ✅ 完全复刻 Excel 结构：使用行号列 + Excel 列名（A, B, C...）
- ✅ 处理合并单元格（只在合并区域的左上角显示值）
- ✅ 日期格式化为 `yyyy-MM-dd HH:mm:ss` 格式
- ✅ 自动转义 Markdown 特殊字符（如管道符 `|`）
- ✅ 自适应列宽格式化
- ✅ 保持原有的空值结构

## 使用方法

### 基本用法

```python
from op_purchase_ai.excel.excel_to_markdown import excel_to_markdown

# 转换 Excel 文件为 Markdown
markdown_output = excel_to_markdown("path/to/your/file.xlsx")
print(markdown_output)
```

### 高级用法

```python
from op_purchase_ai.excel.excel_to_markdown import ExcelToMarkdownConverter

# 创建转换器实例
converter = ExcelToMarkdownConverter("path/to/your/file.xlsx")

# 转换第一个工作表
markdown_output = converter.convert_first_sheet_to_markdown()

# 或者转换指定工作表
specific_sheet = converter.workbook["Sheet2"]
markdown_output = converter.convert_sheet_to_markdown(specific_sheet)
```

### 命令行使用

```bash
python -m op_purchase_ai.excel.excel_to_markdown path/to/your/file.xlsx
```

## 支持的数据类型

| 数据类型 | 输出格式 | 示例 |
|---------|---------|------|
| 文本 | 原样输出 | "产品名称" |
| 数字 | 原样输出 | 123.45 |
| 日期时间 | yyyy-MM-dd HH:mm:ss | 2024-01-15 10:30:00 |
| 日期 | yyyy-MM-dd 00:00:00 | 2024-01-15 00:00:00 |
| 时间 | 1900-01-01 HH:mm:ss | 1900-01-01 14:30:00 |
| 空值 | 空字符串 | |

## 合并单元格处理

- 合并单元格的值只在左上角单元格显示
- 其他被合并的单元格显示为空
- 保持表格结构的完整性

## 示例输出

输入 Excel 表格（A1:D3）：
```
A1: 产品名称    B1: 价格   C1: 创建日期          D1: 备注
A2: 苹果       B2: 5.5    C2: 2024-01-15 10:30  D2: 新鲜水果
A3: [A3:B3合并] B3: --     C3: 2024-01-16        D3: 进口
```

输出 Markdown（完全复刻 Excel 结构）：
```markdown
| 行号  | A       | B   | C                   | D    |
| --- | ------- | --- | ------------------- | ---- |
| 1   | 产品名称    | 价格  | 创建日期                | 备注   |
| 2   | 苹果      | 5.5 | 2024-01-15 10:30:00 | 新鲜水果 |
| 3   | 合并单元格测试 |     | 2024-01-16 00:00:00 | 进口   |
```

## 结构说明

- **行号列**: 第一列显示 Excel 中的实际行号（1, 2, 3...）
- **列名**: 使用 Excel 的标准列名（A, B, C, D...）
- **完全复刻**: 保持与 Excel 完全一致的行列结构
- **合并单元格**: 只在合并区域的左上角单元格显示值，其他位置为空
- **空值保持**: 原 Excel 中的空单元格在 Markdown 中也保持为空

## 注意事项

1. 只处理第一个工作表（除非使用高级 API）
2. 空的工作表会返回 "Empty sheet"
3. 管道符 `|` 会被自动转义为 `\|`
4. 需要安装 `openpyxl` 依赖包
5. 表格结构完全按照 Excel 的行列布局，便于对照和验证
