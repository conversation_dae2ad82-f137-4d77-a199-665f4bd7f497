import os
from typing import List, Dict, TypeVar

from pydantic import BaseModel
from openpyxl import Workbook
from openpyxl.worksheet.worksheet import Worksheet

# 1. 使用 TypeVar 定义一个泛型，并约束它必须是 BaseModel 的子类
# 这使得我们的函数可以接受任何继承自 BaseModel 的模型列表
T = TypeVar('T', bound=BaseModel)


def export_data_to_excel(
        data: List[T],
        column_mapping: Dict[str, str],
        output_path: str,
        sheet_name: str = "Exported Data"
) -> str:
    """
    一个通用的函数，用于将 Pydantic BaseModel 列表导出到 Excel 文件。

    Args:
        data (List[T]): 一个包含 Pydantic BaseModel 实例的列表。
        column_mapping (Dict[str, str]):
            一个字典，用于定义 Excel 的列和数据来源。
            - Key (str): 将在 Excel 中显示的列标题 (e.g., "发票号码")。
            - Value (str): 对应于 BaseModel 中的字段名 (e.g., "invoice_number")。
        output_path (str): 生成的 Excel 文件的保存路径。
        sheet_name (str, optional): Excel 工作表的名称。默认为 "Exported Data"。

    Raises:
        AttributeError: 如果 column_mapping 中指定的字段名在数据模型中不存在。
        IOError: 如果文件无法写入。

    Returns:
        str: 成功保存的 Excel 文件的绝对路径。
    """
    # 健壮性检查：如果数据为空，依然创建一个带表头的空文件
    if not data:
        print("警告: 传入的数据列表为空，将只生成一个包含表头的Excel文件。")
    else:
        # 健壮性检查：在处理前，验证所有需要的字段是否存在于模型中
        first_item = data[0]
        for field_name in column_mapping.values():
            if not hasattr(first_item, field_name):
                raise AttributeError(
                    f"模型 '{type(first_item).__name__}' 中不存在名为 '{field_name}' 的字段。"
                    f"请检查 column_mapping 的值是否正确。"
                )

    # 创建一个新的工作簿
    wb = Workbook()
    try:
        ws: Worksheet = wb.active
        ws.title = sheet_name

        # 1. 写入表头
        headers = list(column_mapping.keys())
        ws.append(headers)

        # 2. 遍历数据并写入行
        field_names = list(column_mapping.values())
        for item in data:
            # [优化] 使用列表推导式使代码更简洁
            row_data = [getattr(item, field, "") for field in field_names]
            ws.append(row_data)

        # 3. 保存文件
        output_dir = os.path.dirname(output_path)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        wb.save(output_path)
        final_path = os.path.abspath(output_path)
        print(f"Excel 文件已成功导出到: {final_path}")
        return final_path

    except IOError as e:
        # 保留原有的异常处理逻辑
        print(f"错误: 无法保存文件到 '{output_path}'. 原因: {e}")
        raise
    finally:
        # [关键修改] 确保工作簿资源在函数退出前被关闭，无论成功还是失败。
        wb.close()
