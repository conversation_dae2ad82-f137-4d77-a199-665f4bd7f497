"""
Excel to Markdown converter with support for merged cells and date formatting.
"""

import datetime
from typing import Any, Dict, List, Optional, Tuple

from openpyxl import load_workbook
from openpyxl.cell import Cell
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet


class ExcelToMarkdownConverter:
    """Convert Excel files to Markdown table format."""

    def __init__(self, excel_file_path: str):
        """
        Initialize the converter with an Excel file.

        Args:
            excel_file_path: Path to the Excel file
        """
        self.excel_file_path = excel_file_path
        self.workbook = load_workbook(excel_file_path, data_only=True)

    def convert_first_sheet_to_markdown(self) -> str:
        """
        Convert the first sheet of the Excel file to Markdown table format.

        Returns:
            Markdown formatted table as string
        """
        # Get the first worksheet
        first_sheet = self.workbook.worksheets[0]
        return self.convert_sheet_to_markdown(first_sheet)

    def convert_sheet_to_markdown(self, sheet: Worksheet) -> str:
        """
        Convert a specific worksheet to Markdown table format.

        Args:
            sheet: The worksheet to convert

        Returns:
            Markdown formatted table as string
        """
        # Get the data range
        max_row = sheet.max_row
        max_col = sheet.max_column

        if max_row == 0 or max_col == 0:
            return "Empty sheet"

        # Build merged cell mapping
        merged_cells_map = self._build_merged_cells_map(sheet)

        # Extract table data
        table_data = []
        for row_idx in range(1, max_row + 1):
            row_data = []
            for col_idx in range(1, max_col + 1):
                cell_value = self._get_cell_value(sheet, row_idx, col_idx, merged_cells_map)
                row_data.append(cell_value)
            table_data.append(row_data)

        # Convert to markdown
        return self._table_data_to_markdown(table_data)

    def _build_merged_cells_map(self, sheet: Worksheet) -> Dict[Tuple[int, int], Tuple[int, int]]:
        """
        Build a mapping of merged cells.

        Args:
            sheet: The worksheet

        Returns:
            Dictionary mapping (row, col) to (start_row, start_col) of merged range
        """
        merged_cells_map = {}

        for merged_range in sheet.merged_cells.ranges:
            start_row = merged_range.min_row
            start_col = merged_range.min_col
            end_row = merged_range.max_row
            end_col = merged_range.max_col

            # Map all cells in the merged range to the top-left cell
            for row in range(start_row, end_row + 1):
                for col in range(start_col, end_col + 1):
                    merged_cells_map[(row, col)] = (start_row, start_col)

        return merged_cells_map

    def _get_cell_value(self, sheet: Worksheet, row: int, col: int,
                       merged_cells_map: Dict[Tuple[int, int], Tuple[int, int]]) -> str:
        """
        Get the value of a cell, handling merged cells and date formatting.

        Args:
            sheet: The worksheet
            row: Row index (1-based)
            col: Column index (1-based)
            merged_cells_map: Mapping of merged cells

        Returns:
            Formatted cell value as string
        """
        # Check if this cell is part of a merged range
        if (row, col) in merged_cells_map:
            source_row, source_col = merged_cells_map[(row, col)]
            # If this is not the top-left cell of the merged range, return empty
            if (row, col) != (source_row, source_col):
                return ""
            # Get value from the source cell
            cell = sheet.cell(row=source_row, column=source_col)
        else:
            cell = sheet.cell(row=row, column=col)

        return self._format_cell_value(cell)

    def _format_cell_value(self, cell: Cell) -> str:
        """
        Format a cell value, handling dates and other data types.

        Args:
            cell: The cell to format

        Returns:
            Formatted value as string
        """
        if cell.value is None:
            return ""

        # Handle datetime objects
        if isinstance(cell.value, datetime.datetime):
            return cell.value.strftime("%Y-%m-%d %H:%M:%S")
        elif isinstance(cell.value, datetime.date):
            return cell.value.strftime("%Y-%m-%d 00:00:00")
        elif isinstance(cell.value, datetime.time):
            return cell.value.strftime("1900-01-01 %H:%M:%S")

        # Handle other types
        return str(cell.value).strip()

    def _table_data_to_markdown(self, table_data: List[List[str]]) -> str:
        """
        Convert table data to Markdown format.

        Args:
            table_data: 2D list of cell values

        Returns:
            Markdown formatted table
        """
        if not table_data:
            return "Empty table"

        # Calculate column widths for better formatting
        col_widths = []
        for col_idx in range(len(table_data[0])):
            max_width = 0
            for row in table_data:
                if col_idx < len(row):
                    max_width = max(max_width, len(row[col_idx]))
            col_widths.append(max(max_width, 3))  # Minimum width of 3

        markdown_lines = []

        # Process each row
        for row_idx, row in enumerate(table_data):
            # Format row cells
            formatted_cells = []
            for col_idx, cell_value in enumerate(row):
                width = col_widths[col_idx] if col_idx < len(col_widths) else 3
                # Escape pipe characters in cell content
                escaped_value = cell_value.replace("|", "\\|")
                formatted_cells.append(f" {escaped_value:<{width}} ")

            # Create the row
            markdown_line = "|" + "|".join(formatted_cells) + "|"
            markdown_lines.append(markdown_line)

            # Add header separator after first row
            if row_idx == 0:
                separator_cells = []
                for width in col_widths:
                    separator_cells.append(f" {'-' * width} ")
                separator_line = "|" + "|".join(separator_cells) + "|"
                markdown_lines.append(separator_line)

        return "\n".join(markdown_lines)


def excel_to_markdown(excel_file_path: str) -> str:
    """
    Convert the first sheet of an Excel file to Markdown table format.

    Args:
        excel_file_path: Path to the Excel file

    Returns:
        Markdown formatted table as string
    """
    converter = ExcelToMarkdownConverter(excel_file_path)
    return converter.convert_first_sheet_to_markdown()


if __name__ == "__main__":
    # Example usage
    import sys

    if len(sys.argv) != 2:
        print("Usage: python excel_to_markdown.py <excel_file_path>")
        sys.exit(1)

    excel_file = sys.argv[1]
    try:
        markdown_output = excel_to_markdown(excel_file)
        print(markdown_output)
    except Exception as e:
        print(f"Error converting Excel file: {e}")
        sys.exit(1)