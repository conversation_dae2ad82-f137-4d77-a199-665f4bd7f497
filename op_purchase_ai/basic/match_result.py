from typing import List, Optional

from pydantic import BaseModel, Field

from op_purchase_ai.basic.point import Point
from op_purchase_ai.basic.rectangle import Rect


class MatchResult(BaseModel):

    confidence: float = Field(..., description="识别结果的置信度")
    x: int = Field(..., description="左上角横坐标")
    y: int = Field(..., description="左上角纵坐标")
    w: int = Field(..., description="宽度")
    h: int = Field(..., description="高度")
    template_scale: float = Field(1, description="模板缩放比例")

    def __repr__(self):
        return '(%.2f, %d, %d, %d, %d, %.2f)' % (self.confidence, self.x, self.y, self.w, self.h, self.template_scale)

    @property
    def left_top(self) -> Point:
        return Point(self.x, self.y)

    @property
    def center(self) -> Point:
        return Point(self.x + self.w // 2, self.y + self.h // 2)

    @property
    def right_bottom(self) -> Point:
        return Point(self.x + self.w, self.y + self.h)

    @property
    def rect(self) -> Rect:
        return Rect(self.x, self.y, self.x + self.w, self.y + self.h)

    def add_offset(self, p: Point):
        self.x += p.x
        self.y += p.y

    @property
    def x1(self) -> int:
        return self.x

    @property
    def y1(self) -> int:
        return self.y

    @property
    def x2(self) -> int:
        return self.x + self.w

    @property
    def y2(self) -> int:
        return self.y + self.h

    def merge(self, other: 'MatchResult') -> None:
        """
        与另一个结果合并
        :param other:
        :return:
        """
        self.x = min(self.x, other.x)
        self.y = min(self.y, other.y)
        self.w = max(self.x + self.w, other.x + other.w) - self.x
        self.h = max(self.y + self.h, other.y + other.h) - self.y


class MatchResultList:
    def __init__(self, only_best: bool = True):
        """
        多个识别结果的组合 适用于一张图中有多个目标结果
        """
        self.only_best: bool = only_best
        self.arr: List[MatchResult] = []
        self.max: Optional[MatchResult] = None

    def __repr__(self):
        return '[%s]' % ', '.join(str(i) for i in self.arr)

    def __iter__(self):
        self.index = 0
        return self

    def __next__(self):
        if self.index < len(self.arr):
            value = self.arr[self.index]
            self.index += 1
            return value
        else:
            raise StopIteration

    def __len__(self):
        return len(self.arr)

    def append(self, a: MatchResult, auto_merge: bool = True, merge_distance: float = 10):
        """
        添加匹配结果，如果开启合并，则保留置信度更高的结果
        :param a: 需要添加的结构
        :param auto_merge: 是否与之前结果进行合并
        :param merge_distance: 多少距离内的
        :return:
        """
        if self.only_best:
            if self.max is None:
                self.max = a
                self.arr.append(a)
            elif a.confidence > self.max.confidence:
                self.max = a
                self.arr[0] = a
        else:
            if auto_merge:
                for i in self.arr:
                    if (i.x - a.x) ** 2 + (i.y - a.y) ** 2 <= merge_distance ** 2:
                        if a.confidence > i.confidence:
                            i.x = a.x
                            i.y = a.y
                            i.confidence = a.confidence
                        return

            self.arr.append(a)
            if self.max is None or a.confidence > self.max.confidence:
                self.max = a

    def __getitem__(self, item):
        return self.arr[item]

    def add_offset(self, lt: Point) -> None:
        """
        给所有结果增加一个左上角的偏移
        用于截取区域后
        """
        for mr in self.arr:
            mr.add_offset(lt)
