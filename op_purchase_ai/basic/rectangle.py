from typing import Union

from pydantic import BaseModel

from op_purchase_ai.basic.point import Point


class Rect(BaseModel):
    x1: int
    y1: int
    x2: int
    y2: int

    def __init__(self, x1: Union[int, float], y1: Union[int, float], x2: Union[int, float], y2: Union[int, float]):
        """
        一个矩形 坐标会转化成整数
        :param x1: 左上角 横坐标
        :param y1: 左上角 纵坐标
        :param x2: 右下角 横坐标
        :param y2: 右下角 纵坐标
        """
        BaseModel.__init__(self, x1=int(x1), y1=int(y1), x2=int(x2), y2=int(y2))

    @property
    def center(self) -> Point:
        return Point((self.x1 + self.x2) // 2, (self.y1 + self.y2) // 2)

    def __repr__(self):
        return '(%d, %d, %d, %d)' % (self.x1, self.y1, self.x2, self.y2)

    @property
    def left_top(self) -> Point:
        return Point(self.x1, self.y1)

    @property
    def right_bottom(self) -> Point:
        return Point(self.x2, self.y2)

    @property
    def width(self) -> int:
        return self.x2 - self.x1

    @property
    def height(self) -> int:
        return self.y2 - self.y1

    def add_offset(self, p: Point):
        self.x1 += p.x
        self.y1 += p.y
        self.x2 += p.x
        self.y2 += p.y

    @property
    def is_valid(self) -> bool:
        """
        是否一个合法的矩形 右下角>左上角
        :return:
        """
        return self.x2 > self.x1 and self.y2 > self.y1

    def merge_rect(self, rect: 'Rect'):
        """
        合并两个矩形
        :param rect:
        :return:
        """
        if not self.is_valid or not rect.is_valid:
            return
        self.x1 = min(self.x1, rect.x1)
        self.y1 = min(self.y1, rect.y1)
        self.x2 = max(self.x2, rect.x2)
        self.y2 = max(self.y2, rect.y2)

    @property
    def area(self) -> int:
        """
        :return: 面积
        """
        return (self.x2 - self.x1) * (self.y2 - self.y1)

    @property
    def rect_text(self) -> str:
        return f'[ {self.x1}, {self.y1}, {self.x2}, {self.y2} ]'
