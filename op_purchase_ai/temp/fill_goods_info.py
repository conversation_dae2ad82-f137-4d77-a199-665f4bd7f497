import io
import os
import random
import time
from concurrent.futures import Thr<PERSON>PoolExecutor, Future
from typing import Optional, Any

import cv2
import numpy as np
import openpyxl
import pandas as pd
from PIL import Image
from cv2.typing import MatLike
from openpyxl.drawing.spreadsheet_drawing import <PERSON>CellAnchor, OneCellAnchor
from openpyxl.utils import column_index_from_string
from openpyxl.worksheet.worksheet import Worksheet

from op_purchase_ai.dify.dify_workflow_client import DifyWorkflowClient
from op_purchase_ai.dify.models.base import FileItem, FileType, ResponseMode, TransferMethod
from op_purchase_ai.dify.models.workflow_run import WorkflowRunCompletionResponse, WorkflowRunRequest
from op_purchase_ai.server.service.op_purchase_dify_service import OpPurchaseDifyService
from op_purchase_ai.utils import cv2_utils, time_utils, env_utils
from op_purchase_ai.utils import os_utils
from op_purchase_ai.utils.log_utils import log

# 辅助函数，用于判断一个值是否为空（None 或空字符串）
def is_empty(value):
    return value is None or (isinstance(value, str) and not value.strip())


class GoodsInfo:
    """存储商品信息的普通类"""

    def __init__(self, row: int):
        """
        构造函数，仅需要行号。
        所有其他属性都初始化为 None。
        """
        self.row: int = row  # 行号

        # 基本信息
        self.sn: Optional[str] = None  # 款号
        self.name: Optional[str] = None  # 商品名称
        self.category: Optional[str] = None  # 商品分类
        self.fabric: Optional[str] = None  # 材质
        self.image: Optional[MatLike] = None  # 商品图片
        self.image_file_path: Optional[str] = None  # 图片路径

        # 详细属性
        self.garment_type: Optional[str] = None  # 上衣类/下衣类
        self.gender: Optional[str] = None  # 性别
        self.shoe_style: Optional[str] = None  # 鞋子款式
        self.monofilament_fineness: Optional[str] = None  # 单丝细度
        self.has_collar: Optional[str] = None  # 是否有领
        self.has_buttons: Optional[str] = None  # 是否有扣
        self.has_zipper: Optional[str] = None  # 是否有拉链
        self.is_lined: Optional[str] = None  # 是否有衬里或夹层
        self.is_full_placket: Optional[str] = None  # 是否全开襟
        self.is_brushed_fabric: Optional[str] = None  # 是否为起绒
        self.is_vest: Optional[str] = None  # 是否背心，如为背心，是否为外穿背心
        self.is_below_hip_length: Optional[str] = None  # 长度是否为臀部以下
        self.has_ribbed_or_cinched_trim: Optional[str] = None  # 袖口/下摆是否有罗纹或其他方式的收紧设计
        self.is_cold_proof: Optional[str] = None  # 是否为防寒衣物

        # 处理标志
        self.handled: bool = False

    def merge_result(self, another: "GoodsInfo", merge_row: bool) -> None:
        """
        合并另一个结果

        Args:
            another: 另一个结果
            merge_row: 是否合并行号

        Returns:
            None
        """
        if merge_row:
            self.row = max(self.row, another.row)
        # --- 基础信息合并 ---
        if is_empty(self.sn): self.sn = another.sn
        if is_empty(self.name): self.name = another.name
        if is_empty(self.category): self.category = another.category
        if is_empty(self.fabric): self.fabric = another.fabric
        if self.image is None: self.image = another.image
        if is_empty(self.image_file_path): self.image_file_path = another.image_file_path

        # --- 详细属性合并 ---
        if is_empty(self.garment_type): self.garment_type = another.garment_type
        if is_empty(self.gender): self.gender = another.gender
        if is_empty(self.shoe_style): self.shoe_style = another.shoe_style
        if is_empty(self.monofilament_fineness): self.monofilament_fineness = another.monofilament_fineness
        if is_empty(self.has_collar): self.has_collar = another.has_collar
        if is_empty(self.has_buttons): self.has_buttons = another.has_buttons
        if is_empty(self.has_zipper): self.has_zipper = another.has_zipper
        if is_empty(self.is_lined): self.is_lined = another.is_lined
        if is_empty(self.is_full_placket): self.is_full_placket = another.is_full_placket
        if is_empty(self.is_brushed_fabric): self.is_brushed_fabric = another.is_brushed_fabric
        if is_empty(self.is_vest): self.is_vest = another.is_vest
        if is_empty(self.is_below_hip_length): self.is_below_hip_length = another.is_below_hip_length
        if is_empty(self.has_ribbed_or_cinched_trim): self.has_ribbed_or_cinched_trim = another.has_ribbed_or_cinched_trim
        if is_empty(self.is_cold_proof): self.is_cold_proof = another.is_cold_proof

        # 如果任意一个对象被处理过，则合并后的对象也标记为已处理
        self.handled = self.handled or another.handled


class GoodsInfoDifyService(OpPurchaseDifyService):

    def __init__(
            self,
            base_url: str
        ) -> None:
        OpPurchaseDifyService.__init__(self, base_url=base_url)

    def _safe_str(self, text: str) -> str:
        if text is None:
            return ''
        return text

    def _inputs(self, goods: GoodsInfo) -> dict[str, Any]:
        inputs: dict[str, Any] = {
            'name': self._safe_str(goods.name),
            'category': self._safe_str(goods.category),
            'fabric': self._safe_str(goods.fabric),
        }
        if goods.image is not None:
            inputs['image'] = FileItem(
                transfer_method=TransferMethod.LOCAL_FILE,
                type=FileType.IMAGE,
                upload_file_path=goods.image_file_path
            )
        return inputs

    def _call(self, op: str, goods: GoodsInfo, api_key: str, enums: list[str] = None) -> str:
        client = DifyWorkflowClient(
            base_url=self.base_url,
            api_key=api_key,
        )

        request = WorkflowRunRequest(
            inputs=self._inputs(goods),
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )

        total_cnt = 0
        while True:
            total_cnt += 1
            log.info(f'[{op}] 入参: {request.model_dump_json()}')
            try:
                response: WorkflowRunCompletionResponse = client.run(
                    request=request
                )
                log.info(f'[{op}] 结果: {response.model_dump_json()}')
                if response.data.status != 'succeeded':
                    # 出现错误 重试一次
                    if response.data.error is not None and response.data.error.find('429') != -1:
                        log.warning(f'[{op}] 大模型限流 当前 {total_cnt} 次')
                        time.sleep(10 + random.randint(0, 5))  # 触发了限流 等待一段时间后再继续
                    else:
                        log.warning(f'[{op}] 运行出错 当前 {total_cnt} 次')
                        time.sleep(random.randint(1, 5))  # 等待一段时间后再继续
                    continue
                else:
                    result: str = response.data.outputs.get('text', '')
                    if enums is not None and result not in enums:
                        log.warning(f'[{op}] 返回结果不符合预期 当前 {total_cnt} 次')
                        time.sleep(random.randint(1, 5))  # 等待一段时间后再继续
                        continue

                    return '' if result == 'None' else result  # 统一使用None作为兜底
            except Exception:
                log.error(f'[{op}] 调用Dify出现异常 当前 {total_cnt} 次', exc_info=True)
                time.sleep(10 + random.randint(0, 5))  # 可能 Dify 挂了 等待一段时间后再继续
                continue

        return ''

    def call_garment_type(self, goods: GoodsInfo) -> str:
        return self._call(op='上衣下衣', goods=goods, api_key='app-TKr4EZq8MGnU0bjqonB2pHA0', enums=['上衣类', '下衣类', 'None'])

    def call_gender(self, goods: GoodsInfo) -> str:
        return self._call(op='性别', goods=goods, api_key='app-xvvikvZLfOAm1kCfd5X43ujs', enums=['男款', '女款', '男女同款'])

    def call_has_collar(self, goods: GoodsInfo) -> str:
        return self._call(op='是否有领', goods=goods, api_key='app-8SEV4FlkanGRCyuqged1s3yx', enums=['是', '否', 'None'])

    def call_has_buttons(self, goods: GoodsInfo) -> str:
        return self._call(op='是否有扣', goods=goods, api_key='app-mP9lZuMsQh2MxOeSBCQMup9z', enums=['是', '否', 'None'])

    def call_has_zipper(self, goods: GoodsInfo) -> str:
        return self._call(op='是否有拉链', goods=goods, api_key='app-8ChJUJ30J2T8OokJDTxx1Hzn', enums=['是', '否', 'None'])

    def call_is_lined(self, goods: GoodsInfo) -> str:
        return self._call(op='是否有衬里或夹层', goods=goods, api_key='app-WL4MiFAGy2P2WEWQLLTpgh4I', enums=['是', '否'])

    def call_is_full_placket(self, goods: GoodsInfo) -> str:
        return self._call(op='是否全开襟', goods=goods, api_key='app-nMSP3DhJ8putoH0U2v3y0WKi', enums=['是', '否', 'None'])

    def call_is_cold_proof(self, goods: GoodsInfo) -> str:
        return self._call(op='是否为防寒衣物', goods=goods, api_key='app-zf8VI1jY7SG57Pw9SzRiyBhT', enums=['是', '否'])

    def call_is_brushed_fabric(self, goods: GoodsInfo) -> str:
        return self._call(op='是否为起绒', goods=goods, api_key='app-uYdxoKMD4h6XwIaGZRa3hJMi', enums=['是', '否', 'None'])

    def call_is_vest(self, goods: GoodsInfo) -> str:
        return self._call(op='是否背心', goods=goods, api_key='app-nTLjsmKYzzrEMMrN9G9M1pux', enums=['是', '否', '外穿背心'])

    def call_is_below_hip_length(self, goods: GoodsInfo) -> str:
        return self._call(op='长度是否为臀部以下', goods=goods, api_key='app-nbJXmpOfOvtyUMfpQi5VfR2A', enums=['是', '否', 'None'])

    def call_has_ribbed_or_cinched_trim(self, goods: GoodsInfo) -> str:
        return self._call(op='袖口/下摆是否有罗纹或其他方式的收紧设计', goods=goods, api_key='app-5ZnVJLq3PnFJa8hhlstgr1g7', enums=['是', '否'])

    def call_monofilament_fineness(self, goods: GoodsInfo) -> str:
        return self._call(op='单丝细度', goods=goods, api_key='app-ThhoelYpp6g6VR41m5xd7XyZ')

    def call_shoe_style(self, goods: GoodsInfo) -> str:
        return self._call(op='鞋子款式', goods=goods, api_key='app-a8tVUkrBkZqzXBOoGSmN0LhC', enums=['未过踝', '过踝但低于小腿', '过踝不低于小腿但未到膝', '过膝', '人字拖', 'None'])


class _ImageInfo:
    """一个辅助类，用于存储图片对象及其在工作表中的行跨度信息。"""
    def __init__(self, image_obj, start_row: int, end_row: int):
        self.image_obj = image_obj
        self.start_row = start_row
        self.end_row = end_row
        self._pil_image: Optional[Image.Image] = None

    def get_pil_image(self) -> Image.Image:
        """
        懒加载方法，仅在需要时将 openpyxl 的图片数据转换为 PIL.Image 对象。
        这可以避免不必要的内存开销。
        """
        if self._pil_image is None:
            image_bytes = self.image_obj._data()
            self._pil_image = Image.open(io.BytesIO(image_bytes))
        return self._pil_image

    def get_cv_image(self) -> MatLike:
        pil_image = self.get_pil_image()
        if pil_image.mode == 'RGBA':
            pil_image = pil_image.convert('RGB')
        rgb = np.array(pil_image)

        # 图像的宽度和高度均应大于10像素，宽高比不应超过200:1或1:200。
        mini = 30.0
        if rgb.shape[0] < mini or rgb.shape[1] < mini:  # 设置为最少20 比较稳
            f = max(mini / rgb.shape[0], mini / rgb.shape[1])
            rgb = cv2.resize(rgb, (0, 0), fx=f, fy=f)
        return rgb

    def __repr__(self) -> str:
        return f"_ImageInfo(start_row={self.start_row}, end_row={self.end_row})"


def map_rows_to_images_in_column(
    sheet: Worksheet,
    image_column: str
) -> dict[int, MatLike]:
    """
    从Excel工作表中提取图片，并根据复杂的匹配逻辑将它们映射到对应的行号。

    此函数执行以下操作：
    1.  从指定工作表中读取所有图片，并能正确处理 OneCellAnchor 和 TwoCellAnchor 两种锚点。
    2.  筛选出所有锚点位置与指定列 (`image_column`) 有重叠的图片。
    3.  计算每张图片的起始和结束行号。
    4.  将图片按“结束行号升序、起始行号升序”的规则进行排序。
    5.  遍历工作表的每一行，并根据您描述的规则将行与排序后的图片进行匹配：
        - 如果当前行被“当前”图片覆盖，则将该图片分配给此行，并将图片指针+1。
        - 如果不匹配，则检查该行是否被“上一张”图片覆盖，如果是，则使用上一张图片，图片指针不变。
        - 如果都不匹配，则该行没有图片，图片指针不变。
    6.  最终返回一个字典，其键是行号，值是对应的 opencv 图片

    Args:
        sheet: Excel工作表对象
        image_column (str): 图片所在的列名 (例如, 'E')。

    Returns:
        dict[int, MatLike]: 一个将行号映射到 opencv 图片对象的字典。
    """
    target_col_idx = column_index_from_string(image_column)
    images_in_col: list[_ImageInfo] = []

    # 步骤 1, 2, 3: 读取、筛选图片并计算行跨度
    for image in sheet._images:
        start_row, end_row = 0, 0
        start_col, end_col = 0, 0

        if isinstance(image.anchor, TwoCellAnchor):
            # openpyxl的行列索引是0-based，转换为1-based
            start_row = image.anchor._from.row + 1
            end_row = image.anchor.to.row + 1
            start_col = image.anchor._from.col + 1
            end_col = image.anchor.to.col + 1
        elif isinstance(image.anchor, OneCellAnchor):
            start_row = image.anchor._from.row + 1
            end_row = start_row  # OneCellAnchor只覆盖一个单元格
            start_col = image.anchor._from.col + 1
            end_col = start_col
        else:
            continue  # 忽略其他类型的锚点

        # 检查图片的水平范围是否与目标列重叠
        if start_col <= target_col_idx <= end_col:
            # 为防止意外情况（如图片被向上拖拽），确保start_row总是小于等于end_row
            if (start_row == end_row
                    and len(images_in_col) > 0
                and images_in_col[-1].end_row == end_row
            ):
                start_row += 1
                end_row += 1
            actual_start_row = min(start_row, end_row)
            actual_end_row = max(start_row, end_row)
            images_in_col.append(_ImageInfo(image, actual_start_row, actual_end_row))

    # 步骤 4: 排序图片
    sorted_images = sorted(images_in_col, key=lambda img: (img.end_row, img.start_row))

    # 步骤 5 & 6: 匹配行与图片，生成最终的映射字典
    row_to_image_map: dict[int, MatLike] = {}
    if not sorted_images:
        return row_to_image_map

    image_idx = 0
    max_sheet_row = sheet.max_row

    for row_num in range(2, max_sheet_row + 1):
        # 找到下一张图片
        while image_idx < len(sorted_images):
            next_image = sorted_images[image_idx]
            if next_image.end_row < row_num:
                image_idx += 1
            else:
                break

        # 如果所有图片都已处理过，只需检查当前行是否仍在最后一张图片的范围内
        if image_idx >= len(sorted_images):
            last_image = sorted_images[-1]
            if last_image.start_row <= row_num <= last_image.end_row:
                row_to_image_map[row_num] = last_image.get_cv_image()
            continue

        current_image = sorted_images[image_idx]
        previous_image = sorted_images[image_idx - 1] if image_idx > 0 else None

        # 规则1: 当前行被“当前”图片覆盖
        if current_image.start_row <= row_num <= current_image.end_row:
            row_to_image_map[row_num] = current_image.get_cv_image()
            image_idx += 1  # 移动到下一张图片
        # 规则2: 当前行被“上一张”图片覆盖
        elif previous_image and (previous_image.start_row <= row_num <= previous_image.end_row):
            row_to_image_map[row_num] = previous_image.get_cv_image()
            # image_idx 保持不变
        # 规则3: 该行没有图片
        else:
            pass

    return row_to_image_map


def read_goods_image(
        excel_file_path: str,
        sn_column_name: str,
        image_column_name: str,
) -> dict[str, MatLike]:
    """
    读取 Excel 文件，遍历里面的sheet，获取不同款号对应的商品图片。

    Args:
        excel_file_path: excel 文件路径
        sn_column_name: 款号对应的列名： A, B, C, AA 这种
        image_column_name: 图片对应的列名： A, B, C, AA 这种

    Returns:
        dict[str, MatLike]: 款号对应的图片
    """
    goods_images = {}

    try:
        # 加载 Excel 工作簿
        workbook = openpyxl.load_workbook(excel_file_path)
        # 将列名转换为列索引 (e.g., 'A' -> 1)
        sn_col_idx = column_index_from_string(sn_column_name)

        # 遍历工作簿中的所有 sheet
        for sheet_name in workbook.sheetnames:
            sheet: Worksheet = workbook[sheet_name]
            row_2_image = map_rows_to_images_in_column(sheet, image_column_name)
            # 遍历所有行 (跳过标题行，假设第一行为标题)
            for row in sheet.iter_rows(min_row=2):
                row_num = row[0].row
                sn_cell = row[sn_col_idx - 1]
                if not sn_cell.value:
                    continue

                sn = str(sn_cell.value)

                cv_image = row_2_image.get(row_num)
                if cv_image is not None:
                    goods_images[sn] = cv_image
    except Exception as e:
        log.error('读取Excel失败', exc_info=True)

    return goods_images


def read_goods_info(
        excel_file_path: str,
        field_columns: dict[str, str]
) -> list[GoodsInfo]:
    """
    读取 Excel 内容

    Args:
        excel_file_path: Excel文件路径
        field_columns: 字段对应的列

    Returns:
        list[GoodsInfo]: 商品信息列表
    """
    goods_info_list: list[GoodsInfo] = []
    try:
        workbook = openpyxl.load_workbook(excel_file_path)
        # 1. 只读取第一个 (活动的) sheet
        sheet = workbook.active

        # 将列名 (e.g., 'A') 转换为列索引 (e.g., 1) 以提高效率
        col_indices = {
            field: column_index_from_string(col_letter)
            for field, col_letter in field_columns.items()
        }

        # 2. 遍历所有行，并跳过第一行标题行 (min_row=2)
        for row_cells in sheet.iter_rows(min_row=2):
            goods_info = GoodsInfo(row=row_cells[0].row)

            # 提取所有文本字段的值
            for field_name, col_idx in col_indices.items():
                cell = row_cells[col_idx - 1]
                value = str(cell.value) if cell.value is not None else None
                # 使用 setattr 动态地为模型实例设置属性
                setattr(goods_info, field_name, value)

            # 创建并添加 GoodsInfo 对象
            goods_info_list.append(goods_info)

    except Exception as e:
        log.error('读取Excel失败', exc_info=True)

    return goods_info_list


def fill_one(
        goods: GoodsInfo,
) -> None:
    """
    调用服务填充商品信息

    Args:
        goods: 商品

    Returns:
        None
    """
    client = GoodsInfoDifyService(base_url=env_utils.get_str('DIFY_API_BASE_URL', 'http://ai-agent.api.vip.com/v1'))
    # 先将图片保存到临时文件夹
    temp_image_path: str | None = None
    if goods.image is not None:
        temp_image_dir = os_utils.get_path_under_work_dir(['.temp', 'vfs'])
        temp_image_name = f'{time_utils.get_full_timestamp_str()}_{goods.row}.png'
        temp_image_path = os.path.join(temp_image_dir, temp_image_name)

        cv2_utils.save_image(goods.image, temp_image_path)
        goods.image_file_path = temp_image_path

    # 调用服务填充信息
    goods.garment_type = client.call_garment_type(goods)
    goods.gender = client.call_gender(goods)
    goods.has_collar = client.call_has_collar(goods)
    goods.has_buttons = client.call_has_buttons(goods)
    goods.has_zipper = client.call_has_zipper(goods)
    goods.is_lined = client.call_is_lined(goods)
    goods.is_full_placket = client.call_is_full_placket(goods)
    goods.is_cold_proof = client.call_is_cold_proof(goods)
    goods.is_brushed_fabric = client.call_is_brushed_fabric(goods)
    goods.is_vest = client.call_is_vest(goods)
    goods.is_below_hip_length = client.call_is_below_hip_length(goods)
    goods.has_ribbed_or_cinched_trim = client.call_has_ribbed_or_cinched_trim(goods)
    goods.monofilament_fineness = client.call_monofilament_fineness(goods)
    goods.shoe_style = client.call_shoe_style(goods)

    if temp_image_path is not None and os.path.exists(temp_image_path):
        os.remove(goods.image_file_path)

    # 处理标记
    goods.handled = True


def fill_all(
        full_excel_path: str,
        image_excel_path_list: list[str],
) -> None:
    """
    读取文件关联后 填充信息 最终导出

    Args:
        full_excel_path: 原文件路径
        image_excel_path_list: 关联图片的excel路径

    Returns:
        None
    """
    full_field_columns = {
        'sn': 'I',
        'name': 'C',
        'category': 'E',
        'fabric': 'F',
    }
    log.info(f'读取商品信息 {full_excel_path}')
    goods_info_list = read_goods_info(full_excel_path, full_field_columns)
    sn_2_goods: dict[str, list[GoodsInfo]] = {}  # 使用列表 防止有重复 后续使用行号做唯一标识
    for goods in goods_info_list:
        if goods.sn not in sn_2_goods:
            sn_2_goods[goods.sn] = []
        sn_2_goods[goods.sn].append(goods)

    for image_excel_path in image_excel_path_list:
        log.info(f'读取商品图片 {image_excel_path}')
        sn_2_image = read_goods_image(image_excel_path, 'D', 'E')
        for sn, goods_list in sn_2_goods.items():
            for goods in goods_list:
                image = sn_2_image.get(goods.sn)
                if image is None:  # 有可能款号存在于多个文件 但有些文件里没图片
                    continue
                goods.image = image

    log.info('读取上次运行记录')
    read_checkpoint(goods_info_list)

    # 按款号合并结果
    sn_2_standard: dict[str, GoodsInfo] = {}
    for sn, sn_goods_list in sn_2_goods.items():
        standard = GoodsInfo(row=0)

        for sn_goods in sn_goods_list:
            standard.merge_result(sn_goods, True)
        sn_2_standard[sn] = standard

    # 将已经处理的结果合并到原结果中
    for sn, sn_goods_list in sn_2_goods.items():
        standard = sn_2_standard.get(sn)
        for sn_goods in sn_goods_list:
            sn_goods.merge_result(standard, False)

    test_sn = [
        '3500876A382',
        'X5C010XG092',
        '9130078P452',
        '3KYH22YNVMZ',
        'X8X007HO089',
        '6GPB40PN28Z',
        '3KYC07YNU1Z',
        '6KZC11ZNAUZ',
        '6KZP37ZNTGZ',
        '3KYP40YNSLZ',
        '3KZQ02ZNJ1Z',
        '7450221A122',
        'B1CC2LB142C',
        'ANA31TA2457',
        '6KAH50AJNRZ',
        '6371741A503',
        '9AL03P9AP07',
        '0NB4CT02060',
        'B1R51PB1P54',
        'ANB01PA2P01',
        'BNB02TB2200',
        'A1J660A1158',
        'ANA33TA2455',
        '8NYABDYJB7Z',
        '6K2N622NJNZ',
        '6K1MUA1MXZZ',
        '3201020A935',
        'X3M329XD223',
        'X1A062XL782',
        '6921336621849027482',
        '6921258366219788044',
        '6921481673628931214',
        'X8X007HO089',
        'B1R51PB1P54'
    ]
    fix_sn_list: list[str] = [
    ]

    total_cnt = 4000
    executor = ThreadPoolExecutor(max_workers=25)
    future_list: list[tuple[GoodsInfo, Future]] = []

    # 先把需要验证的跑了
    for sn, goods in sn_2_standard.items():
        if goods.handled and sn not in fix_sn_list:
            continue

        if goods.sn not in test_sn:
            continue

        future_list.append((goods, executor.submit(fill_one, goods)))

    for sn, goods in sn_2_standard.items():
        if goods.handled:
            continue

        if goods.sn in test_sn:  # 跳过上面已经加入的
            continue

        future_list.append((goods, executor.submit(fill_one, goods)))
        if len(future_list) >= total_cnt:
            break

    log.info(f'当前剩余 {len(future_list)}')

    success_cnt: int = 0
    for standard, f in future_list:
        try:
            f.result()
            success_cnt += 1
            log.info(f'填充商品信息成功 {standard.sn} 当前进度: {success_cnt}/{len(future_list)}')
            sn_goods_list = sn_2_goods.get(standard.sn, [])
            for sn_goods in sn_goods_list:
                sn_goods.merge_result(standard, False)
                append_to_checkpoints(sn_goods)
        except Exception:
            log.error(f'填充商品信息失败 {standard.sn}', exc_info=True)

    log.info('导出结果')
    export_result(full_excel_path, goods_info_list, save_image=False)

def read_checkpoint(goods_info_list: list[GoodsInfo]) -> None:
    """
    读取检查点数据

    Args:
        goods_info_list: 商品列表

    Returns:
        None
    """
    ck_path = _get_checkpoint_path()
    if not os.path.exists(ck_path):
        return
    row_2_goods: dict[int, GoodsInfo] = {goods.row: goods for goods in goods_info_list}
    try:
        df = pd.read_csv(_get_checkpoint_path(), encoding='utf-8')
        df.fillna('', inplace=True)
        for index, row in df.iterrows():
            if row['row'] not in row_2_goods:
                continue
            goods = row_2_goods[row['row']]
            goods.garment_type = row['garment_type']
            goods.gender = row['gender']
            goods.shoe_style = row['shoe_style']
            goods.monofilament_fineness = row['monofilament_fineness']
            goods.has_collar = row['has_collar']
            goods.has_buttons = row['has_buttons']
            goods.has_zipper = row['has_zipper']
            goods.is_lined = row['is_lined']
            goods.is_full_placket = row['is_full_placket']
            goods.is_brushed_fabric = row['is_brushed_fabric']
            goods.is_vest = row['is_vest']
            goods.is_below_hip_length = row['is_below_hip_length']
            goods.has_ribbed_or_cinched_trim = row['has_ribbed_or_cinched_trim']
            goods.is_cold_proof = row['is_cold_proof']
            goods.handled = True
    except Exception as e:
        log.error(f"读取checkpoint失败", exc_info=True)


def _get_checkpoint_path() -> str:
    return os.path.join(os_utils.get_path_under_work_dir(['.temp', 'fill_goods_info']), 'goods_info_checkpoint.csv')


def append_to_checkpoints(goods: GoodsInfo) -> None:
    """
    将当前处理行追加到检查点文件中

    Args:
        goods: 商品

    Returns:
        None
    """
    data = {
        'row': goods.row,
        'garment_type': goods.garment_type,
        'gender': goods.gender,
        'shoe_style': goods.shoe_style,
        'monofilament_fineness': goods.monofilament_fineness,
        'has_collar': goods.has_collar,
        'has_buttons': goods.has_buttons,
        'has_zipper': goods.has_zipper,
        'is_lined': goods.is_lined,
        'is_full_placket': goods.is_full_placket,
        'is_brushed_fabric': goods.is_brushed_fabric,
        'is_vest': goods.is_vest,
        'is_below_hip_length': goods.is_below_hip_length,
        'has_ribbed_or_cinched_trim': goods.has_ribbed_or_cinched_trim,
        'is_cold_proof': goods.is_cold_proof,
        'handled': goods.handled,
    }

    df_one_row = pd.DataFrame([data])

    # 以追加模式写入CSV，第一次写入时包含表头
    checkpoint_file_path = _get_checkpoint_path()
    header_written = os.path.exists(checkpoint_file_path)  # 用于控制CSV表头只写入一次
    try:
        df_one_row.to_csv(
            checkpoint_file_path,
            mode='a',  # 'a' for append
            index=False,
            header=not header_written,  # 只有在第一次写入时才写入header
            encoding='utf-8'
        )
        if not header_written:
            log.info(f"已创建检查点文件并写入第一条记录: {checkpoint_file_path}")
        else:
            log.info(f"已追加新记录到检查点文件 (行号: {goods.row})")

    except Exception as e:
        log.error(f"保存检查点失败 (行号: {goods.row}): {e}")


def export_result(
        full_excel_path: str,
        goods_info_list: list[GoodsInfo],
        save_image: bool,
) -> None:
    """
    读取原Excel文件，将结果写入原文件的列中，然后保存为新的文件

    Args:
        full_excel_path: 原Excel文件
        goods_info_list: 商品信息列表

    Returns:
        None
    """
    row_2_goods: dict[int, GoodsInfo] = {goods.row: goods for goods in goods_info_list}

    workbook = openpyxl.load_workbook(full_excel_path)
    sheet = workbook.active
    image_column_letter = 'AX'
    for row in sheet.iter_rows(min_row=2):
        row_num = row[0].row
        goods = row_2_goods.get(row_num)
        if goods is None or not goods.handled:
            continue

        sheet.cell(row=row_num, column=column_index_from_string('AA')).value = goods.garment_type
        sheet.cell(row=row_num, column=column_index_from_string('AB')).value = goods.is_cold_proof
        sheet.cell(row=row_num, column=column_index_from_string('AD')).value = goods.gender
        sheet.cell(row=row_num, column=column_index_from_string('AE')).value = goods.has_collar
        sheet.cell(row=row_num, column=column_index_from_string('AF')).value = goods.has_buttons
        sheet.cell(row=row_num, column=column_index_from_string('AG')).value = goods.has_zipper
        sheet.cell(row=row_num, column=column_index_from_string('AH')).value = goods.is_lined
        sheet.cell(row=row_num, column=column_index_from_string('AI')).value = goods.is_full_placket
        sheet.cell(row=row_num, column=column_index_from_string('AJ')).value = goods.is_brushed_fabric
        sheet.cell(row=row_num, column=column_index_from_string('AL')).value = goods.is_vest
        sheet.cell(row=row_num, column=column_index_from_string('AN')).value = goods.is_below_hip_length
        sheet.cell(row=row_num, column=column_index_from_string('AO')).value = goods.has_ribbed_or_cinched_trim
        sheet.cell(row=row_num, column=column_index_from_string('AP')).value = goods.monofilament_fineness
        sheet.cell(row=row_num, column=column_index_from_string('AQ')).value = goods.shoe_style

        # 如果存在商品图片，则插入到 AX 列
        if save_image and goods.image is not None:
            try:
                pil_image = Image.fromarray(goods.image)

                # 将 PIL 图像保存到内存中的字节流
                img_byte_arr = io.BytesIO()
                pil_image.save(img_byte_arr, format='PNG')
                xl_image = openpyxl.drawing.image.Image(img_byte_arr)

                # 调整行高和列宽以适应图片
                sheet.row_dimensions[row_num].height = pil_image.height * 0.75
                sheet.column_dimensions[image_column_letter].width = pil_image.width / 7.0

                # 将图片锚定在指定单元格 (OneCellAnchor)
                xl_image.anchor = f'{image_column_letter}{row_num}'
                sheet.add_image(xl_image)
            except Exception as e:
                log.error(f"在第 {row_num} 行插入图片失败: {e}", exc_info=True)

    result_excel_path = full_excel_path.replace('.xlsx', '_result.xlsx')
    workbook.save(result_excel_path)


def __debug_read_goods_image(excel_file_path: str):
    goods_images = read_goods_image(
        excel_file_path,
        'D',
        'E',
    )
    cnt = 0
    for sn, image in goods_images.items():
        cv2_utils.show_image(image, win_name=sn)
        cnt += 1
        if cnt > 5:
            break
    cv2.waitKey(0)
    cv2.destroyAllWindows()


def __debug_read_goods_info():
    excel_file_path: str = os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'fill_goods_info']),
        'full.xlsx'
    )
    goods_info_list = read_goods_info(
        excel_file_path=excel_file_path,
        field_columns={
            'sn': 'I',
            'name': 'C',
            'category': 'E',
            'fabric': 'F',
        }
    )
    log.info(goods_info_list[2])


def __debug_fill_all():
    base_dir = os_utils.get_path_under_work_dir(['.temp', 'fill_goods_info'])
    fill_all(
        full_excel_path=os.path.join(base_dir,'full.xlsx'),
        image_excel_path_list = [
            os.path.join(base_dir,'EA.xlsx'),
            os.path.join(base_dir,'GA.xlsx'),
            os.path.join(base_dir,'EA7 & AX & AC.xlsx'),
        ]
    )

if __name__ == '__main__':
    # __debug_read_goods_info()
    __debug_fill_all()  # 09:49