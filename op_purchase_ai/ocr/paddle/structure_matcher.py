import os
from typing import Optional

from paddleocr import PPStructure

from op_purchase_ai.utils import os_utils


class StructureMatcher:

    def __init__(self,
                 lang: str = 'ch',
                 det_model_name='en_ppocr_mobile_v2.0_table_det',
                 rec_model_name='en_ppocr_mobile_v2.0_table_rec',
                 cls_model_name='ch_ppocr_mobile_v2.0_cls',
                 table_model_name_en='en_ppstructure_mobile_v2.0_SLANet',
                 table_model_name_ch='ch_ppstructure_mobile_v2.0_SLANet',
                 layout_model_name='ppyolov2_r50vd_dcn_365e_tableBank_word',
                 ):
        """
        可选模型见 https://paddlepaddle.github.io/PaddleOCR/latest/ppstructure/models_list.html

        版面识别的效果很差 暂时不采用
        """
        self.lang: str = lang  # 识别语言
        self.det_model_name: str = det_model_name
        self.rec_model_name: str = rec_model_name
        self.cls_model_name: str = cls_model_name
        self.table_model_name: str = table_model_name_ch if self.lang == 'ch' else table_model_name_en
        self.layout_model_name: str = layout_model_name

        self.model: Optional[PPStructure] = None

    def init_model(self) -> None:
        """
        初始化模型
        :return:
        """
        del self.model
        base_dir = os_utils.get_path_under_work_dir(['assets', 'models', 'paddleocr'])
        self.model = PPStructure(
            lang=self.lang,
            det_model_dir=os.path.join(base_dir, self.det_model_name),
            rec_model_dir=os.path.join(base_dir, self.rec_model_name),
            cls_model_dir=os.path.join(base_dir, self.cls_model_name),
            table_model_dir=os.path.join(base_dir, self.table_model_name),
            layout_model_dir=os.path.join(base_dir, self.layout_model_name),
            show_log=False,
            image_orientation=False,
            layout=False,
        )

def __debug():
    import os

    matcher = StructureMatcher(lang='en')
    matcher.init_model()

    from op_purchase_ai.utils import cv2_utils
    img = cv2_utils.read_image(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'pdf2images', 'hcg']),
            'hcg_1.png'
        )
    )
    result = matcher.model(img)
    pass


if __name__ == '__main__':
    __debug()