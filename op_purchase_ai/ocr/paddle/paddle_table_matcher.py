import os

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.utils import os_utils, cv2_utils

if __name__ == '__main__':
    from paddleocr import TableStructureRecognition

    model = TableStructureRecognition(model_name="SLANet")
    test_file_path = os.path.join(
        os_utils.get_path_under_work_dir([".temp", "pdf2images", "smfp"]),
        "test.png"
        )
    output = model.predict(input=test_file_path, batch_size=1)
    mr_list = []
    for res in output:
        res.print(json_format=False)
        res.save_to_json("./output/res.json")
        for bbox in res['bbox']:
            x1 = bbox[0]
            y1 = bbox[1]
            x2 = bbox[4]
            y2 = bbox[5]
            mr_list.append(Rect(x1, y1, x2, y2))
    img = cv2_utils.read_image(test_file_path)
    cv2_utils.show_image(img, mr_list, wait=0)