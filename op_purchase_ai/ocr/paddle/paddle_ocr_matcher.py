import logging
import os
from typing import Optional

from cv2.typing import <PERSON><PERSON><PERSON>
from paddleocr import PaddleOCR

logging.getLogger().handlers.clear()  # 不知道为什么 这里会引入这个logger 清除掉避免console中有重复日志

from op_purchase_ai.ocr.ocr_match_result import Ocr<PERSON>atchResult
from op_purchase_ai.utils import os_utils, cv2_utils
from op_purchase_ai.utils.log_utils import log


class PaddleOcrMatcher:

    """
    OCR识别类 非线程安全
    """

    def __init__(self):
        self.model: Optional[PaddleOCR] = None  # 使用模型

    def init_model(self) -> None:
        """
        加载模型
        :return:
        """
        del self.model

        self.model = PaddleOCR(
            doc_orientation_classify_model_name='PP-LCNet_x1_0_doc_ori',
            doc_orientation_classify_model_dir=os_utils.get_path_under_work_dir(['assets', 'models', 'paddleocr', 'PP-LCNet_x1_0_doc_ori']),
            doc_unwarping_model_name='UVDoc',
            doc_unwarping_model_dir=os_utils.get_path_under_work_dir(['assets', 'models', 'paddleocr', 'UVDoc']),
            text_detection_model_name='PP-OCRv5_server_det',
            text_detection_model_dir=os_utils.get_path_under_work_dir(['assets', 'models', 'paddleocr', 'PP-OCRv5_server_det']),
            textline_orientation_model_name='PP-LCNet_x1_0_textline_ori',
            textline_orientation_model_dir=os_utils.get_path_under_work_dir(['assets', 'models', 'paddleocr', 'PP-LCNet_x1_0_textline_ori']),
            textline_orientation_batch_size=None,
            text_recognition_model_name='PP-OCRv5_server_rec',
            text_recognition_model_dir=os_utils.get_path_under_work_dir(['assets', 'models', 'paddleocr', 'PP-OCRv5_server_rec']),
            text_recognition_batch_size=None,
        )

    def ocr(self, img: MatLike) -> list[OcrMatchResult]:
        """
        对图片进行OCR识别
        :param img: RGB格式的图片
        :return:
        """
        result_list: list[OcrMatchResult] = []
        # 文本检测+文本识别
        ocr_result_list = self.model.predict(
            img,
            use_doc_orientation_classify=False,
            use_textline_orientation=False,
            use_doc_unwarping=False,
        )
        # ocr = PaddleOCR(use_doc_orientation_classify=True, use_doc_unwarping=True) # 文本图像预处理+文本检测+方向分类+文本识别
        # ocr = PaddleOCR(use_doc_orientation_classify=False, use_doc_unwarping=False) # 文本检测+文本行方向分类+文本识别
        ocr_result = ocr_result_list[0]  # 只识别一张图片
        for idx in range(len(ocr_result['rec_texts'])):
            bbox = ocr_result['rec_polys'][idx]
            result_list.append(OcrMatchResult(
                confidence=ocr_result['rec_scores'][idx],
                x=bbox[0][0],
                y=bbox[0][1],
                w=bbox[2][0] - bbox[0][0],
                h=bbox[2][1] - bbox[0][1],
                template_scale=1,
                data=ocr_result['rec_texts'][idx],
            ))
        return result_list


def __debug():
    ocr_matcher = PaddleOcrMatcher()
    ocr_matcher.init_model()
    img = cv2_utils.read_image(os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'pdf2images', 'hh']),
        'hh_2.png'
    ))
    for i in range(10):
        import time
        start_time = time.time()
        result_list = ocr_matcher.ocr(img)
        log.info(f"识别耗时: {time.time() - start_time}")
    log.info(result_list)
    cv2_utils.show_image(img, rects=result_list, max_height=1000, wait=0)


if __name__ == '__main__':
    __debug()
