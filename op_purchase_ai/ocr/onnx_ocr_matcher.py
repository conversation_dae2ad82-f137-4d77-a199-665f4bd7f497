import os
import time

from cv2.typing import Mat<PERSON><PERSON>

from op_purchase_ai.ocr.ocr_match_result import OcrMatchResult
from op_purchase_ai.ocr.onnxocr.onnx_paddleocr import ONNXPaddleOcr
from op_purchase_ai.utils import os_utils, cv2_utils
from op_purchase_ai.utils.log_utils import log


class OnnxOcrMatcher:

    """
    OCR识别类 非线程安全
    https://github.com/jingsongliujing/OnnxOCR
    """

    def __init__(self):
        self.model: ONNXPaddleOcr | None = None

    def init_model(self) -> None:
        """
        加载模型
        :return:
        """
        del self.model
        models_dir = os_utils.get_path_under_work_dir(['assets', 'models', 'onnx_ocr', 'ppocrv5'])
        self.model = ONNXPaddleOcr(
            use_angle_cls=False, use_gpu=False,
            det_model_dir=os.path.join(models_dir, 'det.onnx'),
            rec_model_dir=os.path.join(models_dir, 'rec.onnx'),
            cls_model_dir=os.path.join(models_dir, 'cls.onnx'),
            rec_char_dict_path=os.path.join(models_dir, 'ppocrv5_dict.txt'),
            vis_font_path=os.path.join(models_dir, 'simfang.ttf'),
            det_limit_side_len=1920,  # 图片最长边的限制
        )

    def ocr(self, img: MatLike, cls: bool = False) -> list[OcrMatchResult]:
        """
        对图片进行OCR识别
        :param img: RGB格式的图片
        :param cls: 是否使用方向检测
        :return:
        """
        start_time = time.time()
        result_list: list[OcrMatchResult] = []
        scan_result_list: list = self.model.ocr(img, cls=cls)
        if len(scan_result_list) == 0:
            log.debug('OCR结果 %s 耗时 %.2f', result_list, time.time() - start_time)
            return result_list

        scan_result = scan_result_list[0]
        for anchor in scan_result:
            anchor_position = anchor[0]
            anchor_text = anchor[1][0]
            anchor_score = anchor[1][1]
            result_list.append(
                OcrMatchResult(
                    confidence=anchor_score,
                    x=anchor_position[0][0],
                    y=anchor_position[0][1],
                    w=anchor_position[1][0] - anchor_position[0][0],
                    h=anchor_position[3][1] - anchor_position[0][1],
                    template_scale=1,
                    data=anchor_text,
            ))

        log.debug('OCR结果 %s 耗时 %.2f', result_list, time.time() - start_time)
        return result_list


def __debug():
    ocr_matcher = OnnxOcrMatcher()
    ocr_matcher.init_model()
    img = cv2_utils.read_image(os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'pdf2images', 'tory']),
        'tory_3.png'
    ))
    import time
    start_time = time.time()
    result_list = ocr_matcher.ocr(img)
    log.info(f"识别耗时: {time.time() - start_time}")
    log.info(result_list)
    cv2_utils.show_image(img, rects=result_list, max_height=1000, wait=0)


if __name__ == '__main__':
    __debug()
