from datetime import datetime
from typing import Any, Dict, Optional

from op_purchase_ai.dify.models.base import ResponseMode
from pydantic import BaseModel, ConfigDict, Field


class WorkflowRunRequest(BaseModel):
    """API请求模型"""
    inputs: Dict[str, Any] = Field(..., description="应用定义的变量键值对")
    response_mode: ResponseMode = Field(default=ResponseMode.BLOCKING, description="响应模式")
    user: Optional[str] = Field(default=None, description="用户标识")

    model_config = ConfigDict(use_enum_values=True)


class WorkflowRunCompletionResponseData(BaseModel):
    id: str = Field(..., description="workflow执行ID")
    workflow_id: str = Field(..., description="关联Workflow ID")
    status: str = Field(..., description="执行状态")
    outputs: Optional[Dict[str, Any]] = Field(default=None, description="输出内容")
    error: Optional[str] = Field(default=None, description="错误原因")
    elapsed_time: Optional[float] = Field(default=None, description="耗时(s)")
    total_tokens: Optional[int] = Field(default=None, description="总使用tokens")
    total_steps: int = Field(default=0, description="总步数")
    created_at: datetime = Field(..., description="开始时间")
    finished_at: datetime = Field(..., description="结束时间")


class WorkflowRunCompletionResponse(BaseModel):
    """阻塞模式响应模型"""
    workflow_run_id: str = Field(..., description="workflow执行ID")
    task_id: str = Field(..., description="任务ID")
    data: WorkflowRunCompletionResponseData = Field(..., description="详细内容")


class WorkflowStartedEvent(BaseModel):
    """workflow开始事件模型"""
    task_id: str = Field(..., description="任务ID")
    workflow_run_id: str = Field(..., description="workflow执行ID")
    event: str = Field("workflow_started", description="固定为workflow_started")
    data: Dict[str, Any] = Field(..., description="详细内容")


class NodeStartedEvent(BaseModel):
    """节点开始事件模型"""
    task_id: str = Field(..., description="任务ID")
    workflow_run_id: str = Field(..., description="workflow执行ID")
    event: str = Field("node_started", description="固定为node_started")
    data: Dict[str, Any] = Field(..., description="详细内容")


class TextChunkEvent(BaseModel):
    """文本片段事件模型"""
    task_id: str = Field(..., description="任务ID")
    workflow_run_id: str = Field(..., description="workflow执行ID")
    event: str = Field("text_chunk", description="固定为text_chunk")
    data: Dict[str, Any] = Field(..., description="详细内容")


class NodeFinishedEvent(BaseModel):
    """节点结束事件模型"""
    task_id: str = Field(..., description="任务ID")
    workflow_run_id: str = Field(..., description="workflow执行ID")
    event: str = Field("node_finished", description="固定为node_finished")
    data: Dict[str, Any] = Field(..., description="详细内容")


class WorkflowFinishedEvent(BaseModel):
    """workflow结束事件模型"""
    task_id: str = Field(..., description="任务ID")
    workflow_run_id: str = Field(..., description="workflow执行ID")
    event: str = Field("workflow_finished", description="固定为workflow_finished")
    data: Dict[str, Any] = Field(..., description="详细内容")


class TTSMessageEvent(BaseModel):
    """TTS音频流事件模型"""
    task_id: str = Field(..., description="任务ID")
    message_id: str = Field(..., description="消息唯一ID")
    audio: str = Field(..., description="Base64编码的音频内容")
    created_at: int = Field(..., description="创建时间戳")


class TTSMessageEndEvent(BaseModel):
    """TTS音频流结束事件模型"""
    task_id: str = Field(..., description="任务ID")
    message_id: str = Field(..., description="消息唯一ID")
    audio: str = Field("", description="空字符串")
    created_at: int = Field(..., description="创建时间戳")


class PingEvent(BaseModel):
    """心跳事件模型"""
    event: str = Field("ping", description="固定为ping")


class WorkflowRunChunkCompletionResponse(BaseModel):
    """流式响应块模型"""

    event: str = Field(..., description="事件类型")
    task_id: str = Field(..., description="任务ID")
    workflow_run_id: str = Field(..., description="workflow执行ID")
    data: (
        WorkflowStartedEvent
        | NodeStartedEvent
        | TextChunkEvent
        | NodeFinishedEvent
        | WorkflowFinishedEvent
        | TTSMessageEvent
        | TTSMessageEndEvent
        | PingEvent
    ) = Field(..., description="详细内容")