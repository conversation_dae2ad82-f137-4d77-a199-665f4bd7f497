from enum import Enum
from typing import Optional

from pydantic import BaseModel, ConfigDict, Field


class FileType(str, Enum):
    """支持的文件类型枚举"""

    DOCUMENT = "document"  # 文档类型，'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
    IMAGE = "image"  # 图片类型，'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
    AUDIO = "audio"  # 音频类型，'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
    VIDEO = "video"  # 视频类型，'MP4', 'MOV', 'MPEG', 'MPGA'
    CUSTOM = "custom"  # 自定义类型，others


class TransferMethod(str, Enum):
    """文件传递方式枚举"""

    REMOTE_URL = "remote_url"
    LOCAL_FILE = "local_file"


class ResponseMode(str, Enum):
    """响应模式枚举"""

    STREAMING = "streaming"
    BLOCKING = "blocking"


class FileItem(BaseModel):
    """文件列表项模型"""

    type: FileType = Field(..., description="文件类型")
    transfer_method: TransferMethod = Field(..., description="传递方式")
    url: Optional[str] = Field(default=None, description="文件URL(remote_url时使用)")
    upload_file_id: Optional[str] = Field(
        default=None, description="上传文件ID(local_file时使用) 自己提前上传时使用"
    )
    upload_file_path: Optional[str] = Field(
        default=None, description="上传文件路径(local_file时使用) 传入文件路径自动上传时使用"
    )

    model_config = ConfigDict(use_enum_values=True)
