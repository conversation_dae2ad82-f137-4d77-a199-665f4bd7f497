from pydantic import BaseModel, Field
from datetime import datetime


class FileUploadRequest(BaseModel):
    """
    文件上传请求模型
    """
    file_path: str = Field(..., description="要上传的文件路径")
    user: str = Field(..., description="用户标识，用于定义终端用户的身份")


class FileUploadResponse(BaseModel):
    """
    文件上传响应模型
    """
    id: str = Field(..., description="文件ID")
    name: str = Field(..., description="文件名")
    size: int = Field(..., description="文件大小(byte)")
    extension: str = Field(..., description="文件后缀")
    mime_type: str = Field(..., description="文件mime-type")
    created_by: str = Field(..., description="上传人ID")
    created_at: datetime = Field(..., description="上传时间")