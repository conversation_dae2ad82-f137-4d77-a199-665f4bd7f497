import os

import requests

from op_purchase_ai.dify.models.file import FileUploadRequest, FileUploadResponse
from op_purchase_ai.utils import env_utils, os_utils


class DifyFileClient:

    def __init__(
            self,
            base_url: str,
            api_key: str,
            ) -> None:
        self.base_url: str = base_url
        self.api_key: str = api_key

    def upload(self, request: FileUploadRequest) -> FileUploadResponse:
        """
        Upload a file to Dify service using multipart/form-data format.
        
        The request contains two parts:
        - file: The file to upload, with filename and MIME type
        - user: User identifier, must match the user in message sending API
        
        Example:
            curl -X POST 'http://ai-agent.api.vip.com/v1/files/upload' \
            --header 'Authorization: Bearer {api_key}' \
            --form 'file=@localfile;type=image/[png|jpeg|jpg|webp|gif]' \
            --form 'user=abc-123'

        Args:
            request (FileUploadRequest): File upload request containing file path and user info

        Returns:
            FileUploadResponse: Response containing upload result
        """
        url = f"{self.base_url}/files/upload"
        headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
        
        with open(file=request.file_path, mode='rb') as f:
            files = {
                'file': (os.path.basename(request.file_path), f, self._get_mime_type(request.file_path)),
            }
            data = {
                'user': request.user
            }
            
            response = requests.post(
                url=url,
                headers=headers,
                files=files,
                data=data
            )
            
            response.raise_for_status()
            return FileUploadResponse(**response.json())
        
    def _get_mime_type(self, file_path: str) -> str:
        mime_type = 'application/octet-stream'
        if file_path.endswith('.png'):
            mime_type = 'image/png'
        elif file_path.endswith('.jpeg'):
            mime_type = 'image/jpeg'
        elif file_path.endswith('.jpg'):
            mime_type = 'image/jpeg'
        elif file_path.endswith('.webp'):
            mime_type = 'image/webp'
        elif file_path.endswith('.gif'):
            mime_type = 'image/gif'
            
        return mime_type


def __debug_run():
    client = DifyFileClient(
        base_url=env_utils.get_str('DIFY_API_BASE_URL'),
        api_key=env_utils.get_str('DIFY_GOODS_INFO_EXAMPLE_API_KEY'),
    )
    file_path = os.path.join(
        os_utils.get_path_under_work_dir([".temp", "pdf2images", "hh"]),
        "hh_2.png"
    )
    request = FileUploadRequest(
        file_path=file_path,
        user='abc-123'
    )
    response = client.upload(request)
    print(response)
    print(123)


if __name__ == '__main__':
    __debug_run()