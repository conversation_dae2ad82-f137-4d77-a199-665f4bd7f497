import os
import uuid
from typing import Any

import requests

from op_purchase_ai.dify.dify_file_client import DifyFileClient
from op_purchase_ai.dify.models.base import FileItem, FileType, ResponseMode, TransferMethod
from op_purchase_ai.dify.models.file import FileUploadRequest
from op_purchase_ai.dify.models.workflow_run import (
    WorkflowRunCompletionResponse,
    WorkflowRunRequest,
)
from op_purchase_ai.utils import env_utils, os_utils


class DifyWorkflowClient(DifyFileClient):

    def __init__(
            self,
            base_url: str,
            api_key: str,
            ) -> None:
        DifyFileClient.__init__(
            self=self,
            base_url=base_url,
            api_key=api_key,
        )
        self.base_url: str = base_url
        self.api_key: str = api_key

    def run(self, request: WorkflowRunRequest) -> WorkflowRunCompletionResponse:
        """
        Send http post to run a workflow.

        Example:
            curl -X POST '{base_url}/v1/workflows/run' \
            --header 'Authorization: Bearer {api_key}' \
            --header 'Content-Type: application/json' \
            --data-raw '{
                "inputs": {},
                "response_mode": "streaming",
                "user": "abc-123"
            }'

        Args:
            request (WorkflowRunRequest): Contains workflow run parameters including:
                - inputs: The input data for the workflow
                - response_mode: The response mode (e.g., "streaming")
                - user: The user identifier

        Returns:
            WorkflowRunCompletionResponse: The workflow run completion response

        """
        if request.user is None:
            request.user = str(uuid.uuid4())
        # Check inputs data
        self._check_inputs(
            user=request.user,
            inputs=request.inputs
        )

        url = f"{self.base_url}/workflows/run"
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

        response = requests.post(
            url=url,
            headers=headers,
            json=request.model_dump(),
        )
        response.raise_for_status()

        return WorkflowRunCompletionResponse.model_validate(response.json())
    
    def _check_inputs(self, user: str, inputs: dict[str, Any]) -> None:
        """
        Check inputs data, upload files if needed.

        Args:
            inputs (dict[str, Any]): _description_
        """
        for key, value in inputs.items():
            if isinstance(value, FileItem):
                if (value.transfer_method == TransferMethod.LOCAL_FILE
                    and value.upload_file_path is not None
                    and value.upload_file_id is None
                ):
                    upload_request = FileUploadRequest(
                        file_path=value.upload_file_path,
                        user=user,
                    )
                    upload_response = self.upload(upload_request)
                    value.upload_file_id = upload_response.id


def __debug_run():
    client = DifyWorkflowClient(
        base_url=env_utils.get_str('DIFY_API_BASE_URL'),
        api_key=env_utils.get_str('DIFY_GOODS_INFO_EXAMPLE_API_KEY'),
    )
    test_file_path = os.path.join(
        os_utils.get_path_under_work_dir([".temp", "pdf2images", "hh"]),
        "hh_2.png"
        )
    inputs = {
        'goods_image': FileItem(
            transfer_method=TransferMethod.LOCAL_FILE,
            type=FileType.IMAGE,
            upload_file_path=test_file_path,
            # upload_file_id='2c7c9446-3012-439e-abe0-afed5fb89f90',
        ),
        'image_type': '货单类',
    }
    request = WorkflowRunRequest(
        inputs=inputs,
        response_mode=ResponseMode.BLOCKING,
        user='abc-123'
    )
    try:
        response = client.run(request)
        print(response)
    except Exception as e:
        print(e)


if __name__ == '__main__':
    __debug_run()