import json
from typing import Optional

from op_purchase_ai.basic.rectangle import Rect


def rect_from_str(rect_str: str) -> Optional[Rect]:
    """
    从字符串转变成 Rect
    :param rect_str: 字符串 例子: "[ [0, 0], [1, 1] ]"， "[ 0, 0, 1, 1 ]"
    :return:
    """
    try:
        prop_rect_arr: list = json.loads(rect_str)
        if len(prop_rect_arr) == 4:
            prop_rect = Rect(
                x1=prop_rect_arr[0],
                y1=prop_rect_arr[1],
                x2=prop_rect_arr[2],
                y2=prop_rect_arr[3],
            )
        else:
            if len(prop_rect_arr) < 2:
                return None
            if len(prop_rect_arr[0]) != 2 or len(prop_rect_arr[1]) != 2:
                return None
            prop_rect = Rect(
                x1=prop_rect_arr[0][0],
                y1=prop_rect_arr[0][1],
                x2=prop_rect_arr[1][0],
                y2=prop_rect_arr[1][1],
            )
        if prop_rect.is_valid:
            return prop_rect
        else:
            return None
    except Exception:
        return None


def merge_rect_list(rect_list: list[Rect]) -> Rect:
    """
    将一系列的rect进行合并 取最大范围
    :param rect_list: 矩形列表
    :return: 合并后的结果
    """
    result: Optional[Rect] = None
    for rect in rect_list:
        if result is None:  # 注意要新建一个 merge_rect会改变原来的坐标
            result = Rect(x1=rect.x1, y1=rect.y1, x2=rect.x2, y2=rect.y2)
        else:
            result.merge_rect(rect)

    return result
