import os
from functools import lru_cache


@lru_cache
def get_profile() -> str:
    return os.getenv('PROFILE', 'production')


@lru_cache
def is_dev_profile() -> bool:
    return get_profile() == 'development'


@lru_cache
def uvicorn_port() -> int:
    return int(os.getenv('UVICORN_PORT', 80))

@lru_cache
def get_str(key: str, default: str = '') -> str:
    return os.getenv(key, default)

@lru_cache
def get_int(key: str, default: int = 0) -> int:
    return int(os.getenv(key, default))
