import re
from urllib.parse import urlparse, unquote


def get_domain(url: str):
    """
    获取一个url的域名
    :param url:
    :return:
    """
    # 解析 URL
    parsed_url = urlparse(url)
    # 获取域名（netloc 包含域名和可能的端口号）
    domain = parsed_url.netloc
    # 去除端口号（如果有）
    domain = domain.split(':')[0]
    return domain


def decode_unicode_escapes(s):
    return re.sub(r'\\u([0-9a-fA-F]{4})', lambda m: chr(int(m.group(1), 16)), s)


def url_unquote(url: str) -> str:
    """
    对url进行unquote
    :param url:
    :return:
    """
    return unquote(url)


if __name__ == '__main__':
    print(get_domain('https://static1.adidas.com.cn/t395/MTczNDMxMzY3MDE2M2UwZjJiYTFlLTQ5YzAtNDlhYy1hNTIz.jpeg?im=Resize=(200,200)'))