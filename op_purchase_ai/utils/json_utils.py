import json
from typing import Optional


def safe_load_arr(json_str: str, default_value: Optional[list] = None) -> list:
    """
    安全读取json字符串转变成数组
    :param json_str: json字符串
    :param default_value: 默认值 出现异常时返回
    :return:
    """
    try:
        arr = json.loads(json_str)
        if isinstance(arr, list):
            return arr
        else:
            return default_value
    except Exception:
        return default_value
