import math

from op_purchase_ai.basic.point import Point
from op_purchase_ai.basic.rectangle import Rect


def distance_between(x: Point, y: Point) -> float:
    """
    计算两点之间的距离
    :param x: 点x
    :param y: 点y
    :return: 两点之间的距离
    """
    return math.sqrt((x.x - y.x) ** 2 + (x.y - y.y) ** 2)


def iou(x: Rect, y: Rect) -> float:
    """
    计算两个矩形的重叠面积
    :param x: Rect
    :param y: Rect
    :return: 重叠面积
    """
    x_area = x.area
    y_area = y.area

    if x_area == 0 or y_area == 0:
        return 0

    intersection_area = max(0, min(x.x2, y.x2) - max(x.x1, y.x1)) * max(0, min(x.y2, y.y2) - max(x.y1, y.y1))
    return intersection_area / (x_area + y_area - intersection_area)


def max_overlap_percent(x: Rect, y: Rect) -> float:
    """
    计算两个矩形的交集 占两个矩形占比的最大值
    :param x: 矩形
    :param y: 矩形
    :return: 分别占比的最大值
    """
    i = Rect(
        x1=max(x.x1, y.x1),
        y1=max(x.y1, y.y1),
        x2=min(x.x2, y.x2),
        y2=min(x.y2, y.y2),
    )

    x_area = x.area
    y_area = y.area
    i_area = i.area

    if x_area == 0 or y_area == 0 or i_area == 0:
        return 0

    return max(0.0, i_area * 1.0 / x_area, i_area * 1.0 / y_area)
