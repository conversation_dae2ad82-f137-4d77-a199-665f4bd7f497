import logging


def get_logger():
    logger = logging.getLogger('Op-Purchase-AI')
    logger.handlers.clear()
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter('[%(asctime)s.%(msecs)03d] [%(filename)s %(lineno)d] [%(levelname)s]: %(message)s', '%H:%M:%S')

    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger


def set_log_level(level: int) -> None:
    """
    显示日志等级
    :param level:
    :return:
    """
    log.setLevel(level)
    for handler in log.handlers:
        handler.setLevel(level)


log = get_logger()
