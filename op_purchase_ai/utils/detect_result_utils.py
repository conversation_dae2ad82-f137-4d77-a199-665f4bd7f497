from typing import Optional

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.ocr.ocr_match_result import OcrMatchResult
from op_purchase_ai.server.analyse_file.model.analyse_file_detect_result import AnalyseFileDetectResult
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.utils import str_utils, cal_utils
from op_purchase_ai.yolo.yolo_match_result import YoloMatchR<PERSON>ult


def list_from_text(match_result_list: list[OcrMatchResult], det_type: str) -> list[AnalyseFileDetectResult]:
    """
    转换文本结果
    :param match_result_list: 识别结果
    :param det_type: 识别方式
    :return: 返回结果
    """
    result_list: list[AnalyseFileDetectResult] = []
    for match_result in match_result_list:
        detect_result = AnalyseFileDetectResult(
            det_idx=0,
            det_type=det_type,
            det_rect=match_result.rect.rect_text,
            rec_text=match_result.data,
            rec_type='TEXT',
        )
        detect_result.init_rect()
        result_list.append(detect_result)

    return result_list


def list_from_image(match_result_list: list[YoloMatchResult], det_type: str) -> list[AnalyseFileDetectResult]:
    """
    转换图片结果
    :param match_result_list: 识别结果
    :param det_type: 识别方式
    :return: 返回结果
    """
    result_list: list[AnalyseFileDetectResult] = []
    for match_result in match_result_list:
        detect_result = AnalyseFileDetectResult(
            det_idx=0,
            det_type=det_type,
            det_rect=match_result.rect.rect_text,
            rec_text=match_result.data.class_name,
            rec_type='IMAGE',
        )
        detect_result.init_rect()
        result_list.append(detect_result)

    return result_list


def merge_ocr_to_lines(result_list: list[OcrMatchResult]) -> list[list[OcrMatchResult]]:
    """
    将多个识别结果 按行合并
    :param result_list: 识别结果
    :return: 多行结果 从上到下 从左到右
    """
    # 识别结果按纵坐标排序
    sorted_result_list: list[OcrMatchResult] = [i for i in result_list]
    sorted_result_list.sort(key=lambda x: x.rect.y1)

    # 将识别结果分行
    line_list: list[list[OcrMatchResult]] = []
    last_line_rect: Optional[Rect] = None
    for result in sorted_result_list:
        det_rect = result.rect
        if det_rect is None or not det_rect.is_valid:
            continue

        # 新的一行
        if last_line_rect is None or abs(last_line_rect.center.y - det_rect.center.y) > min(last_line_rect.height, det_rect.height) * 0.5:
            line_list.append([result])
            last_line_rect = Rect(x1=det_rect.x1, y1=det_rect.y1, x2=det_rect.x2, y2=det_rect.y2)
        else:
            line_list[-1].append(result)
            last_line_rect.merge_rect(det_rect)

    for line in line_list:
        line.sort(key=lambda x: x.rect.x1)

    return line_list


def merge_to_lines(detect_result_list: list[AnalyseFileDetectResult]) -> list[list[AnalyseFileDetectResult]]:
    """
    将多个识别结果 按行合并
    :param detect_result_list: 识别结果
    :return: 多行结果 从上到下 从左到右
    """
    # 识别结果按纵坐标排序
    sorted_det_result_list: list[AnalyseFileDetectResult] = []
    for detect_result in detect_result_list:
        if not detect_result.is_rect_valid:
            continue
        sorted_det_result_list.append(detect_result)

    sorted_det_result_list.sort(key=lambda x: x.rect.y1)

    # 将识别结果分行
    line_list: list[list[AnalyseFileDetectResult]] = []
    last_line_rect: Optional[Rect] = None
    for detect_result in sorted_det_result_list:
        det_rect = detect_result.rect
        if det_rect is None or not det_rect.is_valid:
            continue

        # 新的一行
        if last_line_rect is None or abs(last_line_rect.center.y - det_rect.center.y) > min(last_line_rect.height,
                                                                                            det_rect.height) * 0.5:
            line_list.append([detect_result])
            last_line_rect = Rect(x1=det_rect.x1, y1=det_rect.y1, x2=det_rect.x2, y2=det_rect.y2)
        else:
            line_list[-1].append(detect_result)
            last_line_rect.merge_rect(det_rect)

    for line in line_list:
        line.sort(key=lambda x: x.rect.x1)

    return line_list


def find_title_line(line_list: list[list[AnalyseFileDetectResult]],
                    prop_list: list[AnalyseFileGoodsPropExample]) -> list[AnalyseFileDetectResult]:
    """
    在多行识别里 找到属于表头的那行
    :param line_list: 多行识别结果
    :param prop_list: 示例属性列表
    :return: 表头行 是入参数组的其中一个
    """

    # 定位表头行 找到匹配表头列最多的一行
    # 表头列匹配数量相同时 找最上方的一行
    title_line: Optional[list[AnalyseFileDetectResult]] = None
    title_line_match_cnt: int = 0

    for line in line_list:
        line_text_list: list[str] = [i.rec_text for i in line]
        match_cnt: int = 0
        for prop in prop_list:
            prop_idx = str_utils.find_best_match_by_difflib(prop.prop_name, line_text_list)
            if prop_idx is not None and prop_idx >= 0:
                match_cnt += 1

        # 起码要匹配到一半以上的表头列
        if match_cnt > title_line_match_cnt and match_cnt > len(prop_list) * 0.5:
            title_line = line
            title_line_match_cnt = match_cnt

    # 有可能只有第一页有表头 这时候其它页自动补充一个假表头
    if title_line is None:
        title_line = []
        for prop in prop_list:
            title = AnalyseFileDetectResult(
                det_idx=-1,
                det_type='fake',
                det_rect=f'[ [ {prop.rect.x1}, 0 ] , [ {prop.rect.x2}, 1 ] ]',
                rec_type=prop.prop_type,
                rec_text=prop.prop_name
            )
            title.init_rect()
            title_line.append(title)

    return title_line


def get_prop_idx_2_title_idx(title_line: list[AnalyseFileDetectResult],
                             prop_list: list[AnalyseFileGoodsPropExample]) -> dict[int, int]:
    """
    在表头行里 找到 示例属性 对应的 表头列
    :param title_line: 表头行识别结果
    :param prop_list: 示例属性列表
    :return: 示例下标 对应的 表头下标
    """
    prop_idx_2_title_idx: dict[int, int] = {}
    title_text_list: list[str] = [i.rec_text for i in title_line]
    for prop_idx, prop in enumerate(prop_list):
        # 优先使用文本匹配 因为多模态识别表头的坐标有可能错误
        title_idx = str_utils.find_best_match_by_difflib(prop.prop_name, title_text_list)
        if title_idx is not None and title_idx >= 0:
            prop_idx_2_title_idx[prop_idx] = title_idx
            continue

        if prop.is_rect_valid:
            # 如果表头有标注坐标 则使用坐标匹配
            for title_idx, title in enumerate(title_line):
                if prop_idx not in prop_idx_2_title_idx:
                    prop_idx_2_title_idx[prop_idx] = title_idx
                else:
                    old_title = title_line[prop_idx_2_title_idx[prop_idx]]
                    # 表格通常都是左对齐，找最左方最接近的
                    old_dis = abs(prop.rect.x1 - old_title.rect.x1)
                    new_dis = abs(prop.rect.x1 - title.rect.x1)
                    if new_dis < old_dis:
                        prop_idx_2_title_idx[prop_idx] = title_idx

    return prop_idx_2_title_idx


def filter_detect_result(
        ocr_result_list: list[OcrMatchResult],
        yolo_result_list: list[YoloMatchResult],
        text_result_list: list[OcrMatchResult],
        image_result_list: list[YoloMatchResult]
) -> tuple[list[OcrMatchResult], list[YoloMatchResult]]:
    """
    将模型识别结果 和 pdf提取的结果进行比较
    过滤模型识别结果中 与 pdf提取结果重复的部分
    :param ocr_result_list: OCR模型识别结果
    :param yolo_result_list: YOLO模型识别结果
    :param text_result_list: PDF文本提取结果
    :param image_result_list: PDF图片提取结果
    :return: 返回过滤后的模型识别结果
    """
    filter_ocr_result_list: list[OcrMatchResult] = []
    for ocr_result in ocr_result_list:
        existed: bool = False
        for text_result in text_result_list:
            if cal_utils.max_overlap_percent(ocr_result.rect, text_result.rect) > 0.8:
                existed = True
                break

        if not existed:
            filter_ocr_result_list.append(ocr_result)

    filter_yolo_result_list: list[YoloMatchResult] = []
    for yolo_result in yolo_result_list:
        existed: bool = False
        for image_result in image_result_list:
            if cal_utils.max_overlap_percent(yolo_result.rect, image_result.rect) > 0.8:
                existed = True
                image_result.data = yolo_result.data  # 获取YOLO识别结果的分类
                break

        if not existed:
            filter_yolo_result_list.append(yolo_result)

    return filter_ocr_result_list, filter_yolo_result_list