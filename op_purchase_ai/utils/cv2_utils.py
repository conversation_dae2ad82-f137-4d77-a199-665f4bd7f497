import os
from typing import Union, Optional

import cv2
from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.basic.match_result import MatchResult
from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.ocr.ocr_match_result import OcrMatchResult


def read_image(file_path: str) -> Optional[MatLike]:
    """
    读取图片
    :param file_path: 图片路径
    :return:
    """
    if not os.path.exists(file_path):
        return None
    image = cv2.imread(file_path, cv2.IMREAD_UNCHANGED)
    if image is None:  # 有可能文件损毁无法打开
        return None
    if image.ndim == 2:
        return image
    elif image.ndim == 3:
        return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
    elif image.ndim == 4:
        return cv2.cvtColor(image, cv2.COLOR_BGRA2RGB)
    else:
        return image


def save_image(img: MatLike, file_path: str) -> None:
    """
    保存图片
    :param img: RBG格式的图片
    :param file_path: 保存路径
    """
    if img.ndim == 3:
        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
    cv2.imwrite(file_path, img)


def show_image(img: MatLike,
               rects: Union[list[Union[Rect, MatchResult]]] = None,
               win_name: str = 'DEBUG', wait: Optional[int] = None, destroy_after: bool = False,
               max_height: Optional[int] = None):
    """
    显示一张图片
    :param img: 图片
    :param rects: 需要画出来的框
    :param win_name:
    :param wait: 显示后等待按键的秒数
    :param destroy_after: 显示后销毁窗口
    :return:
    """
    to_show = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)

    if rects is not None:
        for rect in rects:
            if isinstance(rect, MatchResult):
                cv2.rectangle(to_show, (rect.x, rect.y), (rect.x + rect.w, rect.y + rect.h), (255, 0, 0), 1)
                if isinstance(rect, OcrMatchResult):
                    cv2.putText(to_show, rect.data, (rect.x, rect.y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 255), 2)
            elif isinstance(rect, Rect):
                cv2.rectangle(to_show, (rect.x1, rect.y1), (rect.x2, rect.y2), (255, 0, 0), 1)

    if max_height is not None and to_show.shape[0] > max_height:
        dsize = (int(max_height * to_show.shape[1] / to_show.shape[0]), max_height)
        to_show = cv2.resize(to_show, dsize)

    cv2.imshow(win_name, to_show)
    if wait is not None:
        cv2.waitKey(wait)
    if destroy_after:
        cv2.destroyWindow(win_name)


def crop_image(img, rect: Rect = None, copy: bool = False) -> tuple[MatLike, Optional[Rect]]:
    """
    裁剪图片 裁剪区域可能超出图片范围
    :param img: 原图
    :param rect: 裁剪区域 (x1, y1, x2, y2)
    :param copy: 是否复制新图
    :return: 裁剪后图片 和 实际的裁剪区域
    """
    if rect is None:
        return (img.copy() if copy else img), None

    x1, y1, x2, y2 = rect.x1, rect.y1, rect.x2, rect.y2
    if x1 < 0:
        x1 = 0
    if x2 > img.shape[1]:
        x2 = img.shape[1]
    if y1 < 0:
        y1 = 0
    if y2 > img.shape[0]:
        y2 = img.shape[0]

    x1, y1 = int(x1), int(y1)
    x2, y2 = int(x2), int(y2)
    crop = img[y1: y2, x1: x2]
    return (crop.copy() if copy else crop), Rect(x1, y1, x2, y2)


def crop_image_only(img, rect: Rect = None, copy: bool = False) -> MatLike:
    """
    裁剪图片 裁剪区域可能超出图片范围
    :param img: 原图
    :param rect: 裁剪区域 (x1, y1, x2, y2)
    :param copy: 是否复制新图
    :return: 只返回裁剪后图片
    """
    return crop_image(img, rect=rect, copy=copy)[0]
