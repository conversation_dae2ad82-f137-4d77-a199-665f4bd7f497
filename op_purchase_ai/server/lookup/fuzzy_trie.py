from dataclasses import dataclass
from typing import List, Any, Dict


@dataclass
class TrieMatchResult:
    """
    模糊匹配的结果封装。

    Attributes:
        text: 匹配到的字典树中的原始单词。
        distance: 查询词与匹配词之间的编辑距离。
        data_list: 该单词在插入时关联的数据列表。
    """
    text: str
    distance: int
    data_list: list[Any]


class TrieNode:
    """字典树（Trie）的节点。"""

    def __init__(self):
        self.children: dict[str, 'TrieNode'] = {}
        self.is_end: bool = False
        self.data_list: list[Any] = []
        self.original_text: str = ""


class FuzzyTrie:
    """
    一个支持模糊搜索的字典树（Trie）结构。

    该实现包含两种高效的模糊搜索模式：
    1. 整词匹配 (search): 查找与查询词近似的完整单词。
    2. 子串匹配 (find_words_containing): 查找包含与查询词近似的子串的单词。

    两种模式都基于优化的 Levenshtein 距离算法（动态规划）实现。
    """

    def __init__(self):
        """初始化一个空的模糊字典树。"""
        self.root = TrieNode()
        # 用于缓存从某个节点出发能找到的所有单词，避免重复的深度优先搜索。
        self._downstream_words_cache: Dict[TrieNode, List[TrieNode]] = {}

    def insert_word(self, word: str, data: Any) -> None:
        """
        向字典树中插入一个单词及其关联数据。

        Args:
            word: 要插入的单词。
            data: 与该单词关联的数据。如果 data 是列表，则会合并；否则，会添加到列表中。
        """
        node = self.root
        for char in word:
            if char not in node.children:
                node.children[char] = TrieNode()
            node = node.children[char]

        node.is_end = True
        if isinstance(data, list):
            node.data_list.extend(data)
        else:
            node.data_list.append(data)
        node.original_text = word

    def search(self, query: str, max_distance: int = 1) -> List[TrieMatchResult]:
        """
        [整词匹配] 搜索与查询词 `query` 近似的完整单词。

        例如: query="apple", 字典树中有 "aple", max_distance=1 -> 匹配。

        Args:
            query: 用于查询的字符串。
            max_distance: 允许的最大编辑距离。

        Returns:
            一个按编辑距离和长度排序的匹配结果列表。

        DP 设计方案:
            - 状态定义: 采用经典的 Levenshtein 距离动态规划。`dp[i][j]` 表示字典树中从根节点到当前节点路径的前 `i` 个字符，
              与查询词 `query` 的前 `j` 个字符之间的最小编辑距离。
            - 状态压缩: 为优化空间，算法只保留 DP 表的上一行 (`prev_dp_row`) 和当前行 (`new_dp_row`)。
              `prev_dp_row[j]` 相当于 `dp[i-1][j]`。
            - 初始化: 初始 DP 行 `initial_dp_row` 为 `[0, 1, 2, ..., len(query)]`。这代表了将一个空字符串（根节点路径）
              转换成 `query` 的前缀所需的代价（即 `j` 次插入操作）。
            - 状态转移: `new_dp_row[j]` 的值由三种操作的最小代价决定：
              1. 替换/匹配: `prev_dp_row[j-1] + cost` (如果字符相同 cost=0, 否则=1)
              2. 插入 (往Trie路径插入，相当于从query删除): `prev_dp_row[j] + 1`
              3. 删除 (从Trie路径删除，相当于往query插入): `new_dp_row[j-1] + 1`
            - 结果判断: 当遍历到一个 `is_end` 为 `True` 的节点时，其对应的 `prev_dp_row` 的最后一个值 `prev_dp_row[-1]`
              即为该完整单词与 `query` 的最终编辑距离。
            - 剪枝: 如果计算出的 `new_dp_row` 中所有值的最小值都已超过 `max_distance`，则从该节点出发的任何更长路径
              都不可能满足条件，因此可以停止对该分支的探索。
        """
        if not query.strip():
            return []

        results = []
        nodes_to_process = [(self.root, list(range(len(query) + 1)))]

        while nodes_to_process:
            current_node, prev_dp_row = nodes_to_process.pop(0)

            if current_node.is_end:
                final_distance = prev_dp_row[-1]
                if final_distance <= max_distance:
                    results.append(TrieMatchResult(
                        text=current_node.original_text,
                        distance=final_distance,
                        data_list=current_node.data_list
                    ))

            for char, child_node in current_node.children.items():
                new_dp_row = [prev_dp_row[0] + 1]

                for i in range(len(query)):
                    substitution_cost = prev_dp_row[i] + (1 if char != query[i] else 0)
                    insertion_cost = new_dp_row[-1] + 1
                    deletion_cost = prev_dp_row[i + 1] + 1
                    new_dp_row.append(min(substitution_cost, insertion_cost, deletion_cost))

                if min(new_dp_row) <= max_distance:
                    nodes_to_process.append((child_node, new_dp_row))

        results.sort(key=lambda x: (x.distance, len(x.text)))
        return results

    def find_words_containing(self, query: str, max_distance: int = 1) -> List[TrieMatchResult]:
        """
        [子串匹配] 搜索包含与查询词 `query` 近似的子串的单词。

        例如: query="vision", 字典树中有 "television", max_distance=0 -> 匹配。

        Args:
            query: 要查找的子串。
            max_distance: 允许的最大编辑距离。

        Returns:
            一个按编辑距离和长度排序的匹配结果列表。

        DP 设计方案:
            - 状态定义: 这是对 Levenshtein 距离的巧妙变种。`dp[i][j]` 表示字典树中从根到当前节点路径的**某个后缀**，
              与查询词 `query` 的前 `j` 个字符之间的最小编辑距离。
            - 状态压缩: 同样使用 `prev_dp_row` 和 `new_dp_row` 进行空间优化。
            - 初始化: `initial_dp_row` 为 `[0, 1, 2, ..., len(query)]`。
            - 状态转移 (核心区别): 在计算子节点的 `new_dp_row` 时，其**第一列 `new_dp_row[0]` 始终被强制设为 0**。
              这相当于允许匹配在字典树的任何深度“免费”开始，即忽略掉当前节点之前的所有路径字符，从而实现了对路径后缀的匹配。
              其余 `new_dp_row[j]` 的计算遵循标准的 Levenshtein 递推公式。
            - 结果判断: 当一个节点的 `prev_dp_row` 的最后一个值 `prev_dp_row[-1]` 小于等于 `max_distance` 时，
              意味着当前节点路径的某个后缀已经成功地与**整个** `query` 完成了模糊匹配。此时，从该节点出发的**所有**
              下游单词都被视为有效匹配结果。
            - 结果去重: 由于一个长单词可能在多个中间节点满足匹配条件，使用 `best_results` 字典来确保每个单词
              只保留其编辑距离最小的一次匹配。 例如 `banana` 匹配 `ana`，会出现两次匹配成功，但只会返回一个 `banana`
        """
        if not query.strip():
            return []

        best_results: Dict[str, TrieMatchResult] = {}
        nodes_to_process = [(self.root, list(range(len(query) + 1)))]

        while nodes_to_process:
            current_node, prev_dp_row = nodes_to_process.pop(0)

            distance = prev_dp_row[-1]
            if distance <= max_distance:
                words_from_here = self._collect_all_words_from(current_node)
                for word_node in words_from_here:
                    if word_node.original_text not in best_results or distance < best_results[
                        word_node.original_text].distance:
                        best_results[word_node.original_text] = TrieMatchResult(
                            text=word_node.original_text,
                            distance=distance,
                            data_list=word_node.data_list
                        )

            for char, child_node in current_node.children.items():
                # 关键：第一列始终为0，允许匹配从任何位置开始
                new_dp_row = [0] * (len(query) + 1)

                for i in range(len(query)):
                    j = i + 1
                    substitution_cost = prev_dp_row[j - 1] + (1 if char != query[i] else 0)
                    deletion_cost = prev_dp_row[j] + 1
                    insertion_cost = new_dp_row[j - 1] + 1
                    new_dp_row[j] = min(substitution_cost, insertion_cost, deletion_cost)

                if min(new_dp_row) <= max_distance:
                    nodes_to_process.append((child_node, new_dp_row))

        sorted_results = sorted(list(best_results.values()), key=lambda x: (x.distance, len(x.text)))
        return sorted_results

    def _collect_all_words_from(self, start_node: TrieNode) -> List[TrieNode]:
        """辅助函数：从一个起始节点，收集所有下游的末端节点（即完整单词）。"""
        if start_node in self._downstream_words_cache:
            return self._downstream_words_cache[start_node]

        collected_nodes = []
        nodes_to_visit = [start_node]
        while nodes_to_visit:
            node = nodes_to_visit.pop(0)
            if node.is_end:
                collected_nodes.append(node)
            nodes_to_visit.extend(node.children.values())

        self._downstream_words_cache[start_node] = collected_nodes
        return collected_nodes


if __name__ == '__main__':
    # 1. 构建字典树
    trie = FuzzyTrie()
    trie.insert_word("television", {"id": 1, "category": "electronics"})
    trie.insert_word("visionary", {"id": 2, "category": "concepts"})
    trie.insert_word("division", {"id": 3, "category": "math"})
    trie.insert_word("apple", {"id": 4, "category": "fruit"})
    trie.insert_word("aple", {"id": 5, "category": "fruit_typo"})

    # 2. 使用 find_words_containing (子串模糊匹配)
    print("--- Testing find_words_containing (substring search) ---")
    # 精确子串匹配
    results_contain = trie.find_words_containing("vision", max_distance=0)
    for r in results_contain:
        print(f"Found '{r.text}' containing 'vision' with distance {r.distance}. Data: {r.data_list}")

    print("-" * 20)

    # 模糊子串匹配
    results_contain_fuzzy = trie.find_words_containing("vison", max_distance=1)  # "vison" is a typo for "vision"
    for r in results_contain_fuzzy:
        print(f"Found '{r.text}' containing 'vison' with distance {r.distance}. Data: {r.data_list}")

    # 3. 使用 search (整词模糊匹配)
    print("\n--- Testing search (whole word search) ---")
    results_search = trie.search("aple", max_distance=1)
    for r in results_search:
        print(f"Found word '{r.text}' similar to 'aple' with distance {r.distance}. Data: {r.data_list}")