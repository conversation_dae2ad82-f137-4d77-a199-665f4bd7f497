import os
from typing import Optional

from openpyxl import Workbook
from openpyxl import load_workbook
from openpyxl.cell.read_only import EmptyCell
from openpyxl.styles import Alignment
from openpyxl.utils import get_column_letter
from openpyxl.worksheet.worksheet import Worksheet
from pydantic import BaseModel, Field

from op_purchase_ai.server.context import AppContext
from op_purchase_ai.server.lookup.fuzzy_trie import FuzzyTrie
from op_purchase_ai.utils import os_utils
from op_purchase_ai.utils.log_utils import log


class LookupTarget(BaseModel):

    excel_row_num: int = Field(..., description='Excel行号')
    lookup_value: str = Field(..., description='待查找的值')


class LookupResult(BaseModel):

    lookup_value: str = Field(default='', description='待查找的值')
    pdf_file_name: str = Field(default='', description='PDF文件名')
    pdf_page: str = Field(default='0', description='PDF页码')
    invoice_number: str = Field(default='', description='发票号')
    excel_file_name: str = Field(default='', description='Excel文件名')
    excel_row: str = Field(default='0', description='Excel行号')
    excel_col: str = Field(default='0', description='Excel列号')
    source_type: str = Field(default='', description='来源类型: excel, pdf, merge')


class LookupService:

    def __init__(self, ctx: AppContext):
        self.ctx: AppContext = ctx

    def lookup_invoice(
            self,
            original_excel_path: str,
            lookup_column: str,
            lookup_file_path_list: list[str],
    ) -> str:
        """
        在查找文件列表中搜索原始Excel中指定字段的值，并将匹配结果添加到新列中

        Args:
            original_excel_path: 原始Excel文件路径
            lookup_column: 需要查找的列名称 A, B, C 这种
            lookup_file_path_list: 需要查找的文件路径列表

        Returns:
            str: 比对结果导出excel的路径
        """
        log.info(f'[Excel查找] 开始处理 原始文件={original_excel_path} 查找列={lookup_column}')

        target_list: list[LookupTarget] = self.get_lookup_targets(original_excel_path, lookup_column)
        log.info(f'[Excel查找] 共收集到 {len(target_list)} 个待查找的值')

        # 初始化匹配结果存储
        # 键是原始Excel的行号，值是所有找到的LookupResult对象的列表
        all_match_results: dict[int, list[LookupResult]] = {}

        # 按文件遍历进行查找
        for file_path in lookup_file_path_list:
            if not os.path.exists(file_path):
                log.warning(f'[Excel查找] 文件不存在: {file_path}')
                continue

            file_name = os.path.basename(file_path)
            log.info(f'[Excel查找] 开始处理文件: {file_name}')

            # 在当前文件中查找所有值
            file_matches: dict[int, list[LookupResult]] = self._search_all_values_in_file(file_path, target_list)

            total_matches_in_file = 0
            # 更新并记录所有匹配结果
            for row_num, results in file_matches.items():
                total_matches_in_file += len(results)

                # 如果是第一次匹配到该行，则初始化列表
                if row_num not in all_match_results:
                    all_match_results[row_num] = []

                # 将当前文件找到的所有匹配项添加到主字典中
                all_match_results[row_num].extend(results)

            log.info(f'[Excel查找] 完成处理文件: {file_name}，本文件匹配 {total_matches_in_file} 个结果')

        # 合并结果
        merged_match_results = self._merge_lookup_results(all_match_results)
        export_path = self._export_lookup_results(original_excel_path, target_list, merged_match_results)

        log.info(f'[Excel查找] 处理完成，结果保存到: {export_path}')

        return export_path

    def get_lookup_targets(self, original_excel_path: str, lookup_column: str,) -> list[LookupTarget]:
        """
        获取文件中需要

        Args:
            original_excel_path: 原始Excel文件路径
            lookup_column: 需要查找的列名称 A, B, C 这种

        Returns:
            list[LookupTarget]: 待查找的目标列表
        """
        wb = None
        target_list: list[LookupTarget] = []
        try:
            # 读取原始Excel文件
            wb = load_workbook(original_excel_path)
            ws = wb.active

            # 收集所有需要查找的值
            for row_num in range(2, ws.max_row + 1):  # 从第2行开始，跳过标题行
                # 构造单元格地址，例如 "A2", "B3" 等
                cell_address = f'{lookup_column}{row_num}'
                lookup_value = ws[cell_address].value
                if lookup_value is None or str(lookup_value).strip() == '':
                    continue
                target_list.append(LookupTarget(
                    excel_row_num=row_num,
                    lookup_value=str(lookup_value).strip(),
                ))

            return target_list
        finally:
            if wb is not None:
                wb.close()

    def _write_results_to_excel(
            self,
            ws: Worksheet,
            lookup_values: dict[int, str],
            all_match_results: dict[int, list[tuple[str, str]]],
            max_col: int
    ):
        """
        将匹配结果写入Excel，支持多行匹配结果

        Args:
            ws: 工作表对象
            lookup_values: 原始查找值
            all_match_results: 所有匹配结果
            max_col: 原始数据的最大列数
        """
        # 先保存原始数据
        original_data = {}
        for row_num in lookup_values.keys():
            row_data = []
            for col in range(1, max_col + 1):
                cell_value = ws.cell(row=row_num, column=col).value
                row_data.append(cell_value)
            original_data[row_num] = row_data

        # 清空原始数据区域（保留标题行）
        for row_num in sorted(lookup_values.keys(), reverse=True):
            ws.delete_rows(row_num, 1)

        # 重新写入数据
        current_row = 2  # 从第2行开始写入数据

        for original_row_num in sorted(lookup_values.keys()):
            match_results = all_match_results.get(original_row_num, [])
            original_row_data = original_data[original_row_num]

            if not match_results:
                # 没有匹配结果，写入原始数据和空的匹配结果
                for col, value in enumerate(original_row_data, 1):
                    ws.cell(row=current_row, column=col, value=value)
                ws.cell(row=current_row, column=max_col + 1, value='')
                ws.cell(row=current_row, column=max_col + 2, value='')
                current_row += 1
            else:
                # 有匹配结果，可能有多行
                for i, (file_name, location) in enumerate(match_results):
                    if i == 0:
                        # 第一行：写入原始数据 + 匹配结果
                        for col, value in enumerate(original_row_data, 1):
                            ws.cell(row=current_row, column=col, value=value)
                    else:
                        # 后续行：原始数据列留空，只写匹配结果
                        for col in range(1, max_col + 1):
                            ws.cell(row=current_row, column=col, value='')

                    # 写入匹配结果
                    ws.cell(row=current_row, column=max_col + 1, value=file_name)
                    ws.cell(row=current_row, column=max_col + 2, value=location)
                    current_row += 1

    def _find_column_index(self, ws: Worksheet, column_name: str) -> Optional[int]:
        """
        在工作表中查找指定列名的索引

        Args:
            ws: 工作表对象
            column_name: 列名

        Returns:
            列索引（1-based），如果未找到返回None
        """
        for col in range(1, ws.max_column + 1):
            cell_value = ws.cell(row=1, column=col).value
            if cell_value and str(cell_value).strip() == column_name:
                return col
        return None

    def _search_all_values_in_file(
            self,
            file_path: str,
            lookup_values: list[LookupTarget]
    ) -> dict[int, list[LookupResult]]:
        """
        在单个文件中搜索所有待查找的值，支持多次匹配和模糊匹配

        Args:
            file_path: 文件路径
            lookup_values: 待查找的值字典 {row_num: lookup_value}

        Returns:
            匹配结果字典 {row_num: [location1, location2, ...]}
        """
        file_ext = os.path.splitext(file_path)[1].lower()

        try:
            if file_ext == '.pdf':
                return self._search_all_values_in_pdf(file_path, lookup_values)
            elif file_ext in ['.xlsx', '.xls']:
                return self._search_all_values_in_excel(file_path, lookup_values)
            else:
                log.warning(f'[Excel查找] 不支持的文件类型: {file_ext}')
                return {}
        except Exception as e:
            log.error(f'[Excel查找] 处理文件失败: {file_path}, 错误: {str(e)}')
            return {}

    def _search_all_values_in_pdf(
        self,
        pdf_path: str,
        lookup_values: list[LookupTarget],
    ) -> dict[int, list[LookupResult]]:
        """
        在PDF文件中搜索所有待查找的值，支持模糊匹配和多次匹配

        Args:
            pdf_path: PDF文件路径
            lookup_values: 待查找的值字典 {row_num: lookup_value}

        Returns:
            匹配结果字典 {row_num: [LookupResult]}
        """
        matches = {}

        try:
            log.info(f'[Excel查找] 开始处理PDF文件')

            pdf_file_name: str = os.path.basename(pdf_path)
            if pdf_file_name.find('_') > -1:
                pdf_file_name = pdf_file_name[pdf_file_name.find('_') + 1:]

            page_2_result = self.ctx.pdf_service.get_word_and_image_by_page(pdf_path)
            trie = FuzzyTrie()
            for page_num, (text_list, image_list) in page_2_result.items():
                result = LookupResult(
                    pdf_file_name=pdf_file_name,
                    pdf_page=str(page_num),
                    source_type='pdf'
                )
                for text_result in text_list:
                    trie.insert_word(text_result.data, data=result)

            # 匹配
            for target in lookup_values:
                result_list = trie.find_words_containing(target.lookup_value, max_distance=0)
                matches[target.excel_row_num] = []
                for result in result_list:
                    matches[target.excel_row_num].extend(result.data_list)
                log.debug(f'[Excel查找] 第{target.excel_row_num}行找到 {len(result_list)} 个匹配')

            return matches

        except Exception as e:
            log.error(f'[Excel查找] PDF搜索失败: {pdf_path}, 错误: {str(e)}')
            return {}

    def _search_all_values_in_excel(self, excel_path: str, lookup_values: list[LookupTarget]) -> dict[int, list[LookupResult]]:
        """
        在Excel文件中搜索所有待查找的值，支持多次匹配

        Args:
            excel_path: Excel文件路径
            lookup_values: 待查找的值字典 {row_num: lookup_value}

        Returns:
            匹配结果字典 {row_num: [location1, location2, ...]}
        """
        matches = {}
        wb = None

        try:
            wb = load_workbook(excel_path, read_only=True)
            ws = wb.active

            total_rows = ws.max_row
            total_cols = ws.max_column
            log.info(f'[Excel查找] Excel文件共 {total_rows} 行 {total_cols} 列，搜索 {len(lookup_values)} 个值')

            # 先找出几个特殊列
            special_cols = [
                self._find_column_index(ws, '_PDF_FILE_NAME'),
                self._find_column_index(ws, '_PDF_PAGE_NUM'),
                self._find_column_index(ws, '_PDF_INVOICE_NUMBER'),
            ]

            # 构建搜索树
            trie = FuzzyTrie()
            for row_num in range(1, total_rows + 1):
                for col_num in range(1, total_cols + 1):
                    if col_num in special_cols:
                        continue
                    result = LookupResult(
                        pdf_file_name=str(ws.cell(row=row_num, column=special_cols[0]).value),
                        pdf_page=str(ws.cell(row=row_num, column=special_cols[1]).value),
                        invoice_number=str(ws.cell(row=row_num, column=special_cols[2]).value),
                        excel_file_name=os.path.basename(excel_path),
                        excel_row=str(row_num),
                        excel_col=str(col_num),
                        source_type='excel'
                    )
                    trie.insert_word(str(ws.cell(row=row_num, column=col_num).value), data=result)

            # 匹配
            for target in lookup_values:
                result_list = trie.find_words_containing(target.lookup_value, max_distance=0)
                matches[target.excel_row_num] = []
                for result in result_list:
                    for item in result.data_list:
                        item.lookup_value = target.lookup_value
                        matches[target.excel_row_num].append(item)
                log.debug(f'[Excel查找] 第{target.excel_row_num}行找到 {len(result_list)} 个匹配')

            return matches

        except Exception as e:
            log.error(f'[Excel查找] Excel搜索失败: {excel_path}, 错误: {str(e)}')
            return {}
        finally:
            if wb is not None:
                wb.close()

    def _generate_result_path(self, original_path: str) -> str:
        """
        生成结果文件路径

        Args:
            original_path: 原始文件路径

        Returns:
            结果文件路径
        """
        original_name = os.path.basename(original_path)
        name_without_ext = os.path.splitext(original_name)[0]
        result_filename = f'{name_without_ext}_lookup_result.xlsx'

        return os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'lookup']),
            result_filename
        )

    def _merge_lookup_results(self, row_2_results: dict[int, list[LookupResult]]) -> dict[int, list[LookupResult]]:
        """
        合并查找结果，使用source_type='excel'的结果去匹配source_type='pdf'的结果，
        如果两者的pdf_file_name和pdf_page一样，则进行合并，source_type设置为'merge'

        Args:
            row_2_results: 每行的匹配结果字典 {excel_row_num: [LookupResult, ...]}

        Returns:
            合并后的结果字典
        """
        merged_results = {}
        
        for row_num, results in row_2_results.items():
            # 分别存储excel和pdf的结果
            excel_results = [r for r in results if r.source_type == 'excel']
            pdf_results = [r for r in results if r.source_type == 'pdf']
            other_results = [r for r in results if r.source_type not in ['excel', 'pdf']]
            
            # 用于存储合并后的结果
            merged_row_results = []
            # 用于跟踪已处理的pdf结果
            processed_pdf_indices = set()
            
            # 遍历excel结果，尝试与pdf结果匹配
            for excel_result in excel_results:
                merged = False
                for i, pdf_result in enumerate(pdf_results):
                    # 检查是否已处理过该pdf结果
                    if i in processed_pdf_indices:
                        continue
                        
                    # 检查是否满足合并条件
                    if (excel_result.pdf_file_name == pdf_result.pdf_file_name and 
                        excel_result.pdf_page == pdf_result.pdf_page):
                        # 创建合并结果
                        merged_result = LookupResult(
                            lookup_value=excel_result.lookup_value,
                            pdf_file_name=excel_result.pdf_file_name,
                            pdf_page=excel_result.pdf_page,
                            invoice_number=pdf_result.invoice_number or excel_result.invoice_number,
                            excel_file_name=excel_result.excel_file_name,
                            excel_row=excel_result.excel_row,
                            excel_col=excel_result.excel_col,
                            source_type='both'
                        )
                        merged_row_results.append(merged_result)
                        processed_pdf_indices.add(i)
                        merged = True
                        break
                
                # 如果没有找到匹配的pdf结果，则保留原始excel结果
                if not merged:
                    merged_row_results.append(excel_result)
            
            # 添加未匹配的pdf结果
            for i, pdf_result in enumerate(pdf_results):
                if i not in processed_pdf_indices:
                    merged_row_results.append(pdf_result)
            
            # 添加其他类型的结果
            merged_row_results.extend(other_results)
            
            merged_results[row_num] = merged_row_results
            
        return merged_results

    def _export_lookup_results(
            self,
            original_excel_path: str,
            target_list: list[LookupTarget],
            row_2_results: dict[int, list[LookupResult]],
    ) -> str:
        """
        导出匹配结果到Excel文件。

        此实现通过直接迭代原始Excel行来优化内存使用，避免一次性将整个文件加载到内存中。
        导出的文件会先包含原始Excel的所有列，然后附加查找结果列。
        如果一个原始行有多条匹配结果，原始数据对应的单元格将跨行合并显示，
        每条匹配结果单独占一行。

        Args:
            original_excel_path: 原始Excel文件路径。
            target_list: 待寻找的目标列表。
            row_2_results: 每行的匹配结果字典 {excel_row_num: [LookupResult, ...]}.

        Returns:
            str: 生成的结果Excel文件路径。
        """
        original_wb = None
        wb = None
        try:
            # 1. 以只读模式打开原始Excel文件
            original_wb = load_workbook(original_excel_path, read_only=True)
            original_ws = original_wb.active

            # 获取原始表头和列数
            original_headers = [cell.value for cell in original_ws[1]]
            num_original_cols = len(original_headers)

            # 为了在遍历时能快速找到原始查找值，将target_list转为字典
            target_map = {t.excel_row_num: t for t in target_list}

            # 2. 创建一个新的工作簿用于导出
            wb = Workbook()
            ws = wb.active
            ws.title = "查找结果"

            # 3. 写入新的组合表头
            result_headers = [
                '原始Excel行号', '原始查找值', 'PDF文件名', 'PDF页码', '发票号',
                '匹配Excel文件名', '匹配Excel行', '匹配Excel列', '匹配来源'
            ]
            ws.append(original_headers + result_headers)

            # 4. 直接遍历原始Excel的行，并写入数据，无需预先构建数据地图
            current_row_index = 2  # 从第2行开始写数据

            row_num: int = 1
            for row in original_ws.iter_rows(min_row=2):
                # 跳过完全为空的行，防止'EmptyCell'对象出错
                row_num += 1

                original_row_num = row_num
                original_row_data = [cell.value for cell in row]

                # 获取该行的匹配结果和原始查找值
                matches = row_2_results.get(original_row_num, [])
                target = target_map.get(original_row_num)
                lookup_value = target.lookup_value if target else ''

                start_merge_row = current_row_index

                if not matches:
                    # Case 1: 没有找到匹配项（或该行本就不是查找目标）
                    # 写入原始数据，结果列只填充行号和查找值
                    result_data = [original_row_num, lookup_value] + [''] * (len(result_headers) - 2)
                    ws.append(original_row_data + result_data)
                    current_row_index += 1
                else:
                    # Case 2: 存在一个或多个匹配项
                    for i, match in enumerate(matches):
                        # 假设 _search_all_values_in_excel 已将列号转为字母
                        # 如果是PDF匹配，excel_col默认为'0'
                        excel_col_letter = match.excel_col if match.excel_col != '0' else ''

                        result_data = [
                            original_row_num,
                            lookup_value,
                            match.pdf_file_name,
                            match.pdf_page,
                            match.invoice_number,
                            match.excel_file_name,
                            match.excel_row,
                            excel_col_letter,
                            match.source_type
                        ]
                        if i == 0:
                            # 第一行匹配：写入原始数据 + 结果数据
                            row_data = original_row_data + result_data
                        else:
                            # 后续匹配行：原始数据部分留空，只写入结果数据
                            row_data = [''] * num_original_cols + result_data
                        ws.append(row_data)

                    end_merge_row = current_row_index + len(matches) - 1

                    if len(matches) > 1:
                        alignment = Alignment(vertical='center')
                        for col_idx in range(1, num_original_cols + 1):
                            ws.merge_cells(start_row=start_merge_row, start_column=col_idx, end_row=end_merge_row,
                                           end_column=col_idx)
                            cell_to_align = ws.cell(row=start_merge_row, column=col_idx)
                            cell_to_align.alignment = alignment

                    current_row_index = end_merge_row + 1

            # 5. 自动调整所有列的宽度
            for col in ws.columns:
                max_length = 0
                column_letter = col[0].column_letter
                for cell in col:
                    try:
                        if cell.value:
                            cell_len = len(str(cell.value).encode('gbk')) if any(
                                '\u4e00' <= char <= '\u9fff' for char in str(cell.value)) else len(str(cell.value))
                            if cell_len > max_length:
                                max_length = cell_len
                    except:
                        pass
                adjusted_width = max_length + 4
                ws.column_dimensions[column_letter].width = adjusted_width

            # 6. 生成并保存结果文件
            result_path = self._generate_result_path(original_excel_path)
            wb.save(result_path)
            log.info(f'[Excel导出] 查找结果已保存到: {result_path}')

            return result_path
        finally:
            if wb:
                wb.close()
            if original_wb:
                original_wb.close()

