import os
import time
from collections import deque

from cv2.typing import <PERSON><PERSON><PERSON>
from fastapi import APIRouter

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.server.analyse_file import analyse_file_const
from op_purchase_ai.server.analyse_file.model.analyse_file_export_request import \
    AnalyseResultExportRequest, AnalyseResultExportResponse
from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResultProp
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.analyse_file.model.analyse_file_task_type import AnalyseFileTaskType
from op_purchase_ai.server.api.model.analyse_file.analyse_file_extract_goods_info import \
    AnalyseFileExtractGoodsInfoRequest, AnalyseFileExtractGoodsInfoResponse
from op_purchase_ai.server.api.model.analyse_file.analyse_file_predict_prop_example import \
    AnalyseFilePredictPropExampleRequest, AnalyseFilePredictPropExampleResponse
from op_purchase_ai.server.api.model.analyse_file.anaylse_file_goods_group import AnalyseFilePredictGoodsGroupRequest, \
    AnalyseFilePredictGoodsGroupResponse, AnalyseFilePredictGoodsGroupNode
from op_purchase_ai.server.context import app_context
from op_purchase_ai.server.response.vfs_response import FileUploadResult
from op_purchase_ai.utils import cv2_utils, basic_utils
from op_purchase_ai.utils.log_utils import log

router = APIRouter(prefix="/api/analyse_file")


@router.post(path='/predict_goods_group', response_model=AnalyseFilePredictGoodsGroupResponse)
def predict_goods_group(request: AnalyseFilePredictGoodsGroupRequest) -> AnalyseFilePredictGoodsGroupResponse:
    """
    分析图片中出现的分组

    Args:
        request: 请求body

    Returns:
        AnalyseFilePredictGoodsGroupResponse: 分组结果

    """
    op = '文件分析 商品分组预测'
    temp_file_path_list: list[str] = []

    try:
        image_file_type = AnalyseFileTaskType[request.image_type].value
        if image_file_type == AnalyseFileTaskType.TABLE.value:
            return AnalyseFilePredictGoodsGroupResponse(code=200, group_prop_list=[])

        image_file_path = app_context.vfs_service.download(fid=request.image_file_id)
        image_file_name_prefix = os.path.splitext(os.path.basename(image_file_path))[0]
        temp_file_path_list.append(image_file_path)
        if not image_file_path.endswith('.png'):
            raise RuntimeError('文件不是png格式')

        root_is_goods_page: bool | None = None
        # 根节点代表整个图片
        group_root = AnalyseFilePredictGoodsGroupNode(group_title='整个文件', sub_group_list=[], file_path=image_file_path)
        # 创建一个双端队列用于BFS
        search_list: deque[AnalyseFilePredictGoodsGroupNode] = deque([group_root])
        # 开始BFS循环，直到队列为空
        while search_list:
            current_node = search_list.popleft()
            log.info(f'[{op}] 开始处理分组 [{current_node.group_title}] from file {os.path.basename(current_node.file_path)}')

            # 调用Dify服务对当前图片进行分组预测
            is_goods_page, sub_group_list = app_context.analyse_file_dify_service.goods_info_predict_group(
                image_file_path=current_node.file_path
            )
            if root_is_goods_page is None:
                root_is_goods_page = is_goods_page
                if not root_is_goods_page:
                    return AnalyseFilePredictGoodsGroupResponse(code=200, group_prop_list=[], is_goods_page=root_is_goods_page)


            # 如果没有返回子分组，说明当前节点是叶子节点，处理结束
            if sub_group_list is None or len(sub_group_list) == 0:
                log.info(f'[{op}] 分组 [{current_node.group_title}] 无子分组，处理结束')
                continue

            # 读取当前节点对应的图片，用于后续切割
            current_image = cv2_utils.read_image(current_node.file_path)
            if current_image is None:
                raise RuntimeError(f'读取图片失败: {current_node.file_path}')

            log.info(f'[{op}] 分组 [{current_node.group_title}] 发现 {len(sub_group_list)} 个子分组，切割图片等待进一步预测')

            # 遍历所有预测出的子分组
            for sub_group in sub_group_list:
                # 根据返回的坐标切割图片
                sub_group_rect: Rect = basic_utils.rect_from_str(sub_group.group_rect)
                sub_image = cv2_utils.crop_image_only(current_image, sub_group_rect)

                # 将切割后的图片保存为新的临时文件
                sub_image_path = os.path.join(
                    os.path.dirname(image_file_path),
                    f'{image_file_name_prefix}_sub_group_{len(temp_file_path_list)}.png'
                )
                cv2_utils.save_image(sub_image, sub_image_path)
                temp_file_path_list.append(sub_image_path)

                # 创建子分组节点
                new_node = AnalyseFilePredictGoodsGroupNode(
                    group_title=sub_group.group_title,
                    sub_group_list=[],
                    file_path=sub_image_path,
                )

                # 建立父子关系，并将新创建的子节点加入BFS队列，以便进行下一轮预测
                current_node.sub_group_list.append(new_node)
                search_list.append(new_node)

        return AnalyseFilePredictGoodsGroupResponse(
            code=200,
            group_prop_list=app_context.analyse_file_dify_service.goods_info_summarize_group(
                image_file_path=image_file_path,
                group_root=group_root
            ),
            is_goods_page=root_is_goods_page
        )
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        return AnalyseFilePredictGoodsGroupResponse(code=500, message=str(e))
    finally:
        log.info(f'[{op}] 执行结束 清理文件')
        for temp_filepath in temp_file_path_list:
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)


@router.post(path='/predict_prop_example', response_model=AnalyseFilePredictPropExampleResponse)
def predict_prop_example(request: AnalyseFilePredictPropExampleRequest) -> AnalyseFilePredictPropExampleResponse:
    """
    分析图片中出现的属性和示例值

    Args:
        request (AnalyseFilePredictPropExampleRequest): 请求body

    Returns:
        AnalyseFilePredictPropExampleResponse: 预测结果
    """
    op = '文件分析 商品属性预测'
    temp_filepath_list: list[str] = []

    try:
        image_file_type = AnalyseFileTaskType[request.image_type].value
        temp_filepath = app_context.vfs_service.download(fid=request.image_file_id)
        temp_filepath_list.append(temp_filepath)
        if not temp_filepath.endswith('.png'):
            raise RuntimeError('文件不是png格式')

        log.info(f'[{op}] 下载分页图片成功 开始预测商品属性 filename={os.path.basename(temp_filepath)}')

        prop_list: list[AnalyseFileGoodsPropExample] = app_context.analyse_file_dify_service.goods_info_predict_example(
            image_file_type=image_file_type,
            image_file_path=temp_filepath,
        )

        return AnalyseFilePredictPropExampleResponse(code=200, prop_list=prop_list)
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        return AnalyseFilePredictPropExampleResponse(code=500, message=str(e))
    finally:
        log.info(f'[{op}] 执行结束 清理文件')
        for temp_filepath in temp_filepath_list:
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)


@router.post(path='/extract_goods_info', response_model=AnalyseFileExtractGoodsInfoResponse)
def extract_goods_info(request: AnalyseFileExtractGoodsInfoRequest) -> AnalyseFileExtractGoodsInfoResponse:
    """
    提取图片中的所有商品信息

    Args:
        request (AnalyseFileExtractGoodsInfoRequest): 请求body

    Returns:
        AnalyseFileExtractGoodsInfoResponse: 商品结果
    """
    op = '文件分析 商品信息提取'
    temp_file_path_list: list[str] = []

    try:
        # 初始化
        for prop in request.prop_list:
            prop.init_rect()

        pdf_file_path = app_context.vfs_service.download(fid=request.pdf_file_id)
        temp_file_path_list.append(pdf_file_path)

        task_type = AnalyseFileTaskType[request.task_param.task_type]
        image_file_path = app_context.vfs_service.download(fid=request.image_file_id)
        temp_file_path_list.append(image_file_path)
        if not image_file_path.endswith('.png'):
            raise RuntimeError('文件不是png格式')

        page_image = cv2_utils.read_image(image_file_path)
        if page_image is None:
            raise RuntimeError('读取图片文件失败')

        log.info(
            f'[{op}] 下载PDF和分页图片成功 开始提取 {os.path.basename(pdf_file_path)} {os.path.basename(image_file_path)}')

        if task_type == AnalyseFileTaskType.TABLE:
            goods_list = app_context.analyse_file_table_service.extract_goods_info(
                pdf_file_path=pdf_file_path,
                pdf_page_num=request.page_num,
                task_type=task_type.value,
                image_file_path=image_file_path,
                prop_list=request.prop_list,
                group_prop_list=request.group_prop_list,
            )
        else:
            goods_list = app_context.analyse_file_goods_service.extract_goods_info(
                task_type=task_type.value,
                image_file_path=image_file_path,
                prop_list=request.prop_list,
                group_prop_list=request.group_prop_list,
            )

        if request.task_param.page_invoice_num:
            invoice_num: str = app_context.analyse_file_dify_service.extract_invoice_num(image_file_path)
            for goods in goods_list:
                goods.prop_list.append(AnalyseFileGoodsResultProp(
                    label=analyse_file_const.PROP_NAME_PDF_INVOICE_NUMBER,
                    value=invoice_num,
                    rect_str='[0, 0, 0, 0]',
                ))

        response = AnalyseFileExtractGoodsInfoResponse(code=200, goods_list=goods_list)
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        response = AnalyseFileExtractGoodsInfoResponse(code=500, message=str(e))
    finally:
        app_context.remove_temp_files(temp_file_path_list)

    return response


@router.post(path='/export_result', response_model=AnalyseResultExportResponse)
def analyse_file_export_result(request: AnalyseResultExportRequest) -> AnalyseResultExportResponse:
    """
    分析文件 - 导出结果
    :param request: 请求body
    :return: 导出文件名称和fid
    """
    op = '文件分析 导出结果'
    temp_file_path_list: list[str] = []
    try:
        page_2_file_path: dict[int, str] = {}
        for page, fid in request.page_fid_map.items():
            page_file_path = app_context.vfs_service.download(fid)
            temp_file_path_list.append(page_file_path)
            if not page_file_path.endswith('.png'):
                raise RuntimeError('文件不是png格式')
            page_2_file_path[int(page)] = page_file_path
            time.sleep(0.1)  # 限制避免下载并发太高

        log.info(f'[{op}] 下载文件成功 开始生成excel filename=%s',
                 [os.path.basename(i) for i in temp_file_path_list])

        page_2_image: dict[int, MatLike] = {}
        for page, page_file_path in page_2_file_path.items():
            page_image = cv2_utils.read_image(page_file_path)
            if page_image is None:
                raise RuntimeError('图片读取失败')
            page_2_image[page] = page_image

        excel_temp_filepath = app_context.analyse_goods_service.export_goods_result(
            file_name=request.file_name,
            page_2_image=page_2_image,
            prop_list=request.prop_list,
            goods_result_list=request.goods_result_list,
        )
        temp_file_path_list.append(excel_temp_filepath)
        excel_file_name = os.path.basename(excel_temp_filepath)
        log.info(f'[{op}] 生成excel成功 开始上传 filename=%s', excel_file_name)

        upload_result: FileUploadResult = app_context.vfs_service.upload(excel_temp_filepath)

        return AnalyseResultExportResponse(code=200, file_name=upload_result.file_name, fid=upload_result.fid)
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        return AnalyseResultExportResponse(code=500, message=str(e))
    finally:
        log.info(f'[{op}] 执行结束 清理文件')
        for page_file_path in temp_file_path_list:
            if os.path.exists(page_file_path):
                os.remove(page_file_path)
