import os

from fastapi import APIRouter

from op_purchase_ai.server.api.model.lookup.lookup_invoice import LookupInvoiceRequest, LookupInvoiceResponse
from op_purchase_ai.server.context import app_context
from op_purchase_ai.server.response.vfs_response import FileUploadResult
from op_purchase_ai.utils.log_utils import log

router = APIRouter(prefix="/api/lookup")


@router.post(path='/invoice', response_model=LookupInvoiceResponse)
def invoice(request: LookupInvoiceRequest) -> LookupInvoiceResponse:
    """
    发票信息查找
    :param request: 请求body
    :return:
    """
    op = '发票信息查找'
    temp_filepath_list: list[str] = []
    try:
        original_excel_path = app_context.vfs_service.download(request.source_file.fid)
        temp_filepath_list.append(original_excel_path)

        lookup_file_path_list: list[str] = []
        for lookup_file_item in request.lookup_file_list:
            temp_filepath = app_context.vfs_service.download(lookup_file_item.fid)
            lookup_file_path_list.append(temp_filepath)
            temp_filepath_list.append(temp_filepath)

        log.info(f'[{op}] 下载文件成功 开始查找 filename=%s',
                 [os.path.basename(i) for i in temp_filepath_list])

        export_path = app_context.lookup_service.lookup_invoice(
            original_excel_path=original_excel_path,
            lookup_column=request.lookup_column,
            lookup_file_path_list=lookup_file_path_list,
        )
        temp_filepath_list.append(export_path)
        upload_result: FileUploadResult = app_context.vfs_service.upload(export_path)
        log.info(f'[{op}] 结果上传成功 fid={upload_result.fid}')

        return LookupInvoiceResponse(code=200, fid=upload_result.fid)
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        return LookupInvoiceResponse(code=500, message=str(e))
    finally:
        app_context.remove_temp_files(temp_filepath_list)
