from pydantic import BaseModel, Field

from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResult
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.analyse_file.model.analyse_file_task_param import AnalyseFileTaskParam
from op_purchase_ai.server.response.common_response import CommonResponse


class AnalyseFileExtractGoodsInfoRequest(BaseModel):

    task_param: AnalyseFileTaskParam = Field(..., description="任务参数")
    pdf_file_id: str = Field(..., description="PDF文件ID")
    page_num: int = Field(..., description="页码 从1开始")
    image_file_id: str = Field(..., description="图片文件ID")
    prop_list: list[AnalyseFileGoodsPropExample] = Field(default=[], description="商品属性示例列表")
    group_prop_list: list[AnalyseFileGoodsPropExample] = Field(default=[], description="分组属性列表")


class AnalyseFileExtractGoodsInfoResponse(CommonResponse):

    goods_list: list[AnalyseFileGoodsResult] = Field(default=[], description="商品信息列表")
