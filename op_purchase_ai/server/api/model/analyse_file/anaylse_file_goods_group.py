from pydantic import BaseModel, Field

from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.response.common_response import CommonResponse


class AnalyseFilePredictGoodsGroupRequest(BaseModel):

    image_file_id: str = Field(..., description="图片文件ID")
    image_type: str = Field(..., description="图片类型: EXCEL表格类/货单类/货单类(无商品图)")


class AnalyseFilePredictGoodsGroupResponse(CommonResponse):

    is_goods_page: bool = Field(default=False, description="是否为商品页")
    group_prop_list: list[AnalyseFileGoodsPropExample] = Field(default=[], description="分组属性列表")


class AnalyseFilePredictGoodsGroupDifyResult(BaseModel):
    group_title: str = Field(..., description="分组标题")
    group_rect: str = Field(..., description="分组位置坐标")


class AnalyseFilePredictGoodsGroupNode(BaseModel):

    group_title: str = Field(..., description="分组标题")
    sub_group_list: list["AnalyseFilePredictGoodsGroupNode"] = Field(default=[], description="子分组列表")
    file_path: str | None = Field(default=None, description="切分后的图片文件路径")
