from pydantic import BaseModel, Field

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.ocr.ocr_match_result import OcrMatchResult
from op_purchase_ai.server.response.common_response import CommonResponse
from op_purchase_ai.utils import basic_utils


class AnalyseFileOcrTaskPageRequest(BaseModel):

    image_file_id: str = Field(..., description="图片文件ID")
    rect_str: str | None = Field(default=None, description='识别结果的区域矩形坐标文本')

    rect: Rect | None = Field(default=None, description='识别结果的区域矩形 由rect_str转化 请求入口处应该做好初始化')

    @property
    def is_rect_valid(self) -> bool:
        return self.rect is not None and self.rect.is_valid

    def init_rect(self) -> None:
        self.rect = basic_utils.rect_from_str(self.rect_str)


class AnalyseFileOcrTaskPageResponse(CommonResponse):

    result_list: list[OcrMatchResult] = Field(default=[], description="OCR识别结果列表")
