from pydantic import BaseModel, Field

from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.response.common_response import CommonResponse


class AnalyseFilePredictPropExampleRequest(BaseModel):

    image_file_id: str = Field(..., description="图片文件ID")
    image_type: str = Field(..., description="图片类型: EXCEL表格类/货单类/货单类(无商品图)")


class AnalyseFilePredictPropExampleResponse(CommonResponse):

    prop_list: list[AnalyseFileGoodsPropExample] = Field(default=[], description="商品属性示例列表")
