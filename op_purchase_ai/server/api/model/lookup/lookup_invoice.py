from pydantic import BaseModel, Field

from op_purchase_ai.server.api.model.common.vfs import VfsFileItem
from op_purchase_ai.server.response.common_response import CommonResponse


class LookupInvoiceRequest(BaseModel):

    source_file: VfsFileItem = Field(..., description="Excel文件")
    lookup_column: str = Field(..., description="需要查找的列名称 A, B, C 这种")
    lookup_file_list: list[VfsFileItem] = Field(default=[], description="需要匹配的文件列表")


class LookupInvoiceResponse(CommonResponse):

    fid: str = Field(default='', description="导出文件的fid")
