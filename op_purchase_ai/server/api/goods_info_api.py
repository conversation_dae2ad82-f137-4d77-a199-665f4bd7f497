import json
import os
import shutil
import zipfile

from fastapi import APIRouter

from op_purchase_ai.server.goods_info.entity.make_images_zip import GIMakeImagesZipRequest, GIMakeImagesZipResponse
from op_purchase_ai.server.goods_info.entity.web_image_url_extract import GIWebImageUrlExtractRequest, \
    GIWebImageUrlExtractResponse
from op_purchase_ai.utils import cv2_utils, os_utils
from op_purchase_ai.utils.log_utils import log
from op_purchase_ai.server.context import app_context

router = APIRouter(prefix="/api/goods_info")

@router.post(path='/web_image_url_extract', response_model=GIWebImageUrlExtractResponse)
def goods_info_web_image_url_extract(request: GIWebImageUrlExtractRequest) -> GIWebImageUrlExtractResponse:
    """
    商品信息 - 提取图片url
    :param request: 请求body
    :return: 图片url列表
    """
    if request is None or request.fid is None:
        return GIWebImageUrlExtractResponse(code=400, message='未传入fid')

    op = '商品信息 分析网页内容提取图片链接'
    temp_filepath_list: list[str] = []
    try:
        html_filepath = app_context.vfs_service.download(request.fid)
        temp_filepath_list.append(html_filepath)

        url_list = app_context.crawl_service.extract_image_url_from_html(
            request.goods_url, html_filepath)
        return GIWebImageUrlExtractResponse(code=200, image_url_list=url_list)
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        return GIWebImageUrlExtractResponse(code=500, message=str(e))
    finally:
        log.info(f'[{op}] 执行结束 清理文件')
        for temp_filepath in temp_filepath_list:
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)


@router.post(path='/make_images_zip', response_model=GIMakeImagesZipResponse)
def goods_info_make_images_zip(request: GIMakeImagesZipRequest) -> GIMakeImagesZipResponse:
    """
    商品信息 - 制作图片压缩包
    :param request: 请求body
    :return: 图片url列表
    """
    if request is None or request.image_fid_list is None or len(request.image_fid_list) == 0:
        return GIMakeImagesZipResponse(code=400, message='未传入图片fid')

    # 清除后续用于打包的目录
    zip_dir_path = os_utils.get_path_under_work_dir(
        ['.temp', 'goods_info_zip', request.zip_name])
    shutil.rmtree(zip_dir_path, ignore_errors=True)
    os.mkdir(zip_dir_path)

    op = '商品信息 制作图片压缩包'
    temp_filepath_list: list[str] = []
    try:
        for fid in request.image_fid_list:
            temp_filepath = app_context.vfs_service.download(fid)
            temp_filepath_list.append(temp_filepath)

        # 筛选图片
        target_filepath_list: list[str] = []
        target_size = json.loads(request.image_size)
        width_min = target_size[0]
        width_max = target_size[1]
        height_min = target_size[2]
        height_max = target_size[3]
        for temp_filepath in temp_filepath_list:
            image = cv2_utils.read_image(temp_filepath)
            if image is None:
                log.warning(f'[{op}] 图片损毁 filepath={temp_filepath}')
                continue
            if not (height_min <= image.shape[0] <= height_max and width_min <= image.shape[1] <= width_max):
                continue
            target_filepath_list.append(temp_filepath)

        # 复制文件到待打包区域
        copy_filepath_list: list[str] = []
        for temp_filepath in target_filepath_list:
            new_filepath = os.path.join(
                zip_dir_path, os.path.basename(temp_filepath))
            shutil.copyfile(temp_filepath, new_filepath)
            temp_filepath_list.append(new_filepath)
            copy_filepath_list.append(new_filepath)

        zip_filepath = os.path.join(zip_dir_path, f'{request.zip_name}.zip')
        # 创建ZIP文件
        with zipfile.ZipFile(zip_filepath, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for image_path in copy_filepath_list:
                # 获取相对路径（保持文件夹结构）
                rel_path = os.path.relpath(image_path, zip_dir_path)
                zipf.write(image_path, rel_path)
                log.info(f"[{op}] 压缩包添加: {rel_path}")

        temp_filepath_list.append(zip_filepath)
        update_result = app_context.vfs_service.upload(zip_filepath)

        return GIMakeImagesZipResponse(code=200, fid=update_result.fid)
    except Exception as e:
        log.error(f'[{op}] 执行失败', exc_info=True)
        return GIMakeImagesZipResponse(code=500, message=str(e))
    finally:
        log.info(f'[{op}] 执行结束 清理文件')
        for temp_filepath in temp_filepath_list:
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
