import os
from typing import Optional

from fastapi import APIRouter

from op_purchase_ai.server.context import app_context
from op_purchase_ai.server.request.image_request import ImageRequest
from op_purchase_ai.server.response.ocr_response import OcrResponse
from op_purchase_ai.server.response.yolo_response import YoloResponse
from op_purchase_ai.utils import cv2_utils
from op_purchase_ai.utils.log_utils import log

router = APIRouter(prefix="/api/image")


@router.post(path='/ocr', response_model=OcrResponse)
def image_ocr(request: ImageRequest) -> OcrResponse:
    """
    使用OCR对图片进行文本识别
    :param request: vfs系统的文件ID
    :return:
    """
    if request is None or request.fid is None:
        return OcrResponse(code=400, message='未传入fid')

    fid = request.fid
    temp_filepath: Optional[str] = None
    try:
        temp_filepath = app_context.vfs_service.download(fid)
        if not temp_filepath.endswith('.png'):
            raise RuntimeError('文件不是png格式')

        log.info('[OCR识别] 下载文件成功 开始识别 fid=%s filename=%s',
                 fid, os.path.basename(temp_filepath))
        image = cv2_utils.read_image(temp_filepath)
        if image is None:
            raise RuntimeError('图片读取失败')

        ocr_result_list = app_context.ocr_service.ocr(image)

        return OcrResponse(code=200, result=ocr_result_list)
    except Exception as e:
        log.error('[OCR识别] 识别失败 fid=%s', fid, exc_info=True)
        return OcrResponse(code=500, message=str(e))
    finally:
        if temp_filepath is not None and os.path.exists(temp_filepath):
            log.info('[OCR识别] 识别结束 清理文件 fid=%s filename=%s',
                     fid, os.path.basename(temp_filepath))
            os.remove(temp_filepath)


@router.post(path='/yolo', response_model=YoloResponse)
def image_yolo(request: ImageRequest) -> YoloResponse:
    """
    使用Yolo对图片进行目标检测
    :param request: vfs系统的文件ID
    :return:
    """
    if request is None or request.fid is None:
        return YoloResponse(code=400, message='未传入fid')

    fid = request.fid
    temp_filepath_list: list[str] = []
    try:
        temp_filepath = app_context.vfs_service.download(fid)
        if not temp_filepath.endswith('.png'):
            raise RuntimeError('文件不是png格式')
        temp_filepath_list.append(temp_filepath)
        temp_filename = os.path.basename(temp_filepath)

        log.info('[YOLO目标检测] 下载文件成功 开始检测 fid=%s filename=%s',
                 fid, temp_filename)
        page_image = cv2_utils.read_image(temp_filepath)
        if page_image is None:
            raise RuntimeError('图片读取失败')

        result_list = app_context.yolo_service.run(page_image)

        return YoloResponse(code=200, result=result_list)
    except Exception as e:
        log.error('[YOLO目标检测] 检测失败 fid=%s', fid, exc_info=True)
        return YoloResponse(code=500, message=str(e))
    finally:
        log.info('[YOLO目标检测] 检测结束 清理文件 fid=%s 个数=%d',
                 fid, len(temp_filepath_list))
        for temp_filepath in temp_filepath_list:
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)