from fastapi import APIRouter
import os
import time


from op_purchase_ai.server.response.pdf_response import Pdf2ImagesResponse, Pdf2ImagesRequest
from op_purchase_ai.utils.log_utils import log
from op_purchase_ai.server.context import app_context

router = APIRouter(prefix="/api/pdf")


@router.post(path='/to_images', response_model=Pdf2ImagesResponse)
def pdf_to_images(request: Pdf2ImagesRequest) -> Pdf2ImagesResponse:
    """
    PDF图片切割
    :param request: vfs系统的文件ID
    :return:
    """
    if request is None or request.fid is None:
        return Pdf2ImagesResponse(code=400, message='未传入fid')

    fid = request.fid
    temp_filepath_list: list[str] = []
    try:
        temp_filepath = app_context.vfs_service.download(fid)
        temp_filepath_list.append(temp_filepath)
        if not temp_filepath.endswith('.pdf'):
            raise RuntimeError('文件不是pdf格式')

        log.info('[PDF图片切割] 下载文件成功 开始切割 fid=%s filename=%s',
                 fid, os.path.basename(temp_filepath))

        image_filepath_list = app_context.pdf_service.pdf_to_images(
            pdf_filepath=temp_filepath,
            page_start=request.page_start,
            page_end=request.page_end,
        )
        log.info('[PDF图片切割] 切割完成 开始上传 filename=%s 共%d页 ',
                 os.path.basename(temp_filepath), len(image_filepath_list))

        for page_num, image_filepath in image_filepath_list:
            temp_filepath_list.append(image_filepath)

        response = Pdf2ImagesResponse(result={}, code=200)
        for page_num, image_filepath in image_filepath_list:
            response.result[page_num] = app_context.vfs_service.upload(image_filepath)
            time.sleep(0.2)  # 控制 QPS=10

        return response
    except Exception as e:
        log.error('[PDF图片切割] 切割失败 fid=%s', fid, exc_info=True)
        return Pdf2ImagesResponse(code=500, message=str(e))
    finally:
        log.info('[PDF图片切割] 切割结束 清理文件')
        for temp_filepath in temp_filepath_list:
            if os.path.exists(temp_filepath):
                os.remove(temp_filepath)
