from op_purchase_ai.server.analyse_file.model.analyse_file_detect_result import AnalyseFileDetectResult
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.analyse_file.utils import analyse_file_table_utils
from op_purchase_ai.server.context import AppContext
from op_purchase_ai.utils import cv2_utils, detect_result_utils
from op_purchase_ai.utils.log_utils import log
from op_purchase_ai.yolo.yolo_match_result import YoloMatchResult


class AnalyseFileTableService:

    def __init__(self, ctx: AppContext):
        self.ctx: AppContext = ctx

    def extract_goods_info(
            self,
            task_type: str,
            pdf_file_path: str,
            pdf_page_num: int,
            image_file_path: str,
            prop_list: list[AnalyseFileGoodsPropExample],
            group_prop_list: list[AnalyseFileGoodsPropExample],
    ):
        page_image = cv2_utils.read_image(image_file_path)
        ocr_result_list = self.ctx.ocr_service.ocr(page_image)
        log.info(f'[分页信息提取] 完成OCR识别 共计 {len(ocr_result_list)} 个结果')

        yolo_result_list = self.ctx.yolo_service.run(page_image)
        log.info(f'[分页信息提取] 完成YOLO识别 共计 {len(yolo_result_list)} 个结果')

        text_result_list, image_result_list = self.ctx.pdf_service.extract_word_and_image(
            pdf_file_path=pdf_file_path,
            page_num=pdf_page_num,
            page_image=page_image,
        )
        log.info(
            f'[分页信息提取] 完成文本和图片提取 共计 {len(text_result_list)} 个文本 {len(image_result_list)} 个图片')

        filter_ocr_result_list, filter_yolo_result_list = detect_result_utils.filter_detect_result(
            ocr_result_list, yolo_result_list, text_result_list, image_result_list
        )
        log.info(
            f'[分页信息提取] 合并文本和图片结果 共计 {len(filter_ocr_result_list)} 个文本 {len(filter_yolo_result_list)} 个图片')

        detect_result_list: list[AnalyseFileDetectResult] = (
                detect_result_utils.list_from_text(filter_ocr_result_list, 'AUTO_TEXT_OCR')
                + detect_result_utils.list_from_text(text_result_list, 'AUTO_TEXT_PDF')
                + detect_result_utils.list_from_image(filter_yolo_result_list, 'AUTO_IMAGE_YOLO')
                + detect_result_utils.list_from_image(image_result_list, 'AUTO_IMAGE_PDF')
        )

        predict_image_list: list[YoloMatchResult] = self.ctx.analyse_goods_service.predict_image(
            task_type=task_type,
            page_image=page_image,
            prop_list=prop_list,
            detect_result_list=detect_result_list,
        )
        detect_result_list.extend(detect_result_utils.list_from_image(predict_image_list, 'AUTO_IMAGE_PREDICT'))

        return analyse_file_table_utils.extract_goods_from_table(
            prop_list=prop_list,
            detect_result_list=detect_result_list,
        )