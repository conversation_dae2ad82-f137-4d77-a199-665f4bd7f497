from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.server.analyse_file.model.analyse_file_detect_result import AnalyseFileDetectResult
from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResult, \
    AnalyseFileGoodsResultProp
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.utils import basic_utils, detect_result_utils
from op_purchase_ai.utils.log_utils import log


def extract_goods_from_table(
        prop_list: list[AnalyseFileGoodsPropExample],
        detect_result_list: list[AnalyseFileDetectResult],
) -> list[AnalyseFileGoodsResult]:
    """
    表格类的文件 提取多行结果
    :param prop_list: 所需提取的属性列表
    :param detect_result_list: 识别提取的属性值
    :return:
    """
    # 合并多行结果
    detect_line_list: list[list[AnalyseFileDetectResult]] = detect_result_utils.merge_to_lines(detect_result_list)

    # 找到表头行
    title_line = detect_result_utils.find_title_line(detect_line_list, prop_list)

    # 验证表头区域
    title_rect: Rect = basic_utils.merge_rect_list([i.rect for i in title_line])
    if title_rect is None or not title_rect.is_valid:
        raise Exception('表头行区域异常')

    # 建立属性与表头的映射关系
    prop_idx_2_title_idx, title_idx_2_prop = _build_title_prop_mapping(prop_list, title_line)

    # 处理数据行并转换为商品结果
    result_list = _process_data_lines(
        detect_line_list=detect_line_list,
        title_line=title_line,
        title_rect=title_rect,
        prop_list=prop_list,
        prop_idx_2_title_idx=prop_idx_2_title_idx,
        title_idx_2_prop=title_idx_2_prop,
    )

    # 将属性较少的行合并到最近的属性较多的行上
    merge_result_list: list[AnalyseFileGoodsResult] = _merge_incomplete_results_to_nearby(
        result_list=result_list,
    )

    # 过滤属性较少的行
    return _filter_incomplete_results(merge_result_list)


def _build_title_prop_mapping(
        prop_list: list[AnalyseFileGoodsPropExample],
        title_line: list[AnalyseFileDetectResult]
) -> tuple[dict[int, int], dict[int, AnalyseFileGoodsPropExample]]:
    """
    建立属性与表头的映射关系
    :param prop_list: 属性列表
    :param title_line: 表头行
    :return: (属性索引->表头索引, 表头索引->属性)
    """
    prop_idx_2_title_idx: dict[int, int] = detect_result_utils.get_prop_idx_2_title_idx(title_line, prop_list)

    title_idx_2_prop: dict[int, AnalyseFileGoodsPropExample] = {}
    for prop_idx, prop in enumerate(prop_list):
        title_idx: int = prop_idx_2_title_idx.get(prop_idx)
        if title_idx is None:
            continue
        title_idx_2_prop[title_idx] = prop

    return prop_idx_2_title_idx, title_idx_2_prop


def _process_data_lines(
        detect_line_list: list[list[AnalyseFileDetectResult]],
        title_line: list[AnalyseFileDetectResult],
        title_rect: Rect,
        prop_list: list[AnalyseFileGoodsPropExample],
        prop_idx_2_title_idx: dict[int, int],
        title_idx_2_prop: dict[int, AnalyseFileGoodsPropExample]
) -> list[AnalyseFileGoodsResult]:
    """
    处理数据行并转换为商品结果
    :return: 商品结果列表
    """
    result_list: list[AnalyseFileGoodsResult] = []

    for detect_line in detect_line_list:
        # 验证行的有效性
        if not _is_valid_data_line(detect_line, title_rect):
            continue

        # 建立列映射关系
        title_idx_2_det_idx = _build_column_mapping(detect_line, title_line, title_idx_2_prop)

        # 转换为商品信息
        result = _convert_line_to_goods_result(
            detect_line=detect_line,
            prop_list=prop_list,
            prop_idx_2_title_idx=prop_idx_2_title_idx,
            title_idx_2_det_idx=title_idx_2_det_idx,
        )

        if result.prop_cnt > 0:  # 只保留有属性值的行
            result_list.append(result)

    return result_list


def _is_valid_data_line(line: list[AnalyseFileDetectResult], title_rect: Rect) -> bool:
    """
    验证数据行是否有效
    :param line: 数据行
    :param title_rect: 表头区域
    :return: 是否有效
    """
    line_rect: Rect = basic_utils.merge_rect_list([i.rect for i in line])
    if line_rect is None or not line_rect.is_valid:
        log.error('识别结果行出现非法的矩形区域')
        return False
    if line_rect.center.y < title_rect.y2:  # 过滤表头行上方的
        return False
    return True


def _build_column_mapping(
        detect_line: list[AnalyseFileDetectResult],
        title_line: list[AnalyseFileDetectResult],
        title_idx_2_prop: dict[int, AnalyseFileGoodsPropExample]
) -> dict[int, int]:
    """
    建立表头列与识别结果列的映射关系
    :param detect_line: 当前数据行
    :param title_line: 表头行
    :param title_idx_2_prop: 表头索引到属性的映射
    :return: 表头列索引->识别结果列索引
    """
    # 第一步：建立初始映射（表头列 -> 识别列）
    title_idx_2_det_idx: dict[int, int] = {}
    for title_idx, title in enumerate(title_line):
        if not title.is_rect_valid:
            continue

        title_prop: AnalyseFileGoodsPropExample = title_idx_2_prop.get(title_idx)
        best_det_idx = _find_best_matching_detection(detect_line, title, title_prop)

        if best_det_idx is not None:
            title_idx_2_det_idx[title_idx] = best_det_idx

    # 第二步：解决冲突（一个识别结果对应多个表头列）
    return _resolve_column_conflicts(title_idx_2_det_idx, detect_line, title_line)


def _find_best_matching_detection(
        line: list[AnalyseFileDetectResult],
        title: AnalyseFileDetectResult,
        title_prop: AnalyseFileGoodsPropExample
) -> int:
    """
    为表头列找到最佳匹配的识别结果 距离最近的
    :param line: 数据行
    :param title: 表头列
    :param title_prop: 表头对应的属性
    :return: 最佳匹配的识别结果索引
    """
    best_det_idx = None
    best_distance = float('inf')

    for det_idx, det_result in enumerate(line):
        # 校验类型匹配
        if title_prop is not None and title_prop.prop_type != det_result.rec_type:
            continue

        # 计算位置距离
        distance = _calculate_col_distance(det_result.rect, title.rect)

        # 选择距离最近的
        if distance < best_distance:
            best_distance = distance
            best_det_idx = det_idx

    return best_det_idx


def _resolve_column_conflicts(
        title_idx_2_det_idx: dict[int, int],
        line: list[AnalyseFileDetectResult],
        title_line: list[AnalyseFileDetectResult]
) -> dict[int, int]:
    """
    解决列映射冲突，确保每个识别结果只对应一个表头列
    :param title_idx_2_det_idx: 表头列到识别列的映射
    :param line: 数据行
    :param title_line: 表头行
    :return: 解决冲突后的映射
    """
    # 反向映射：识别列 -> 表头列
    det_idx_2_title_idx: dict[int, int] = {}
    for title_idx, det_idx in title_idx_2_det_idx.items():
        if det_idx not in det_idx_2_title_idx:
            det_idx_2_title_idx[det_idx] = title_idx
        else:
            # 发生冲突，选择距离更近的
            det_result = line[det_idx]
            old_title_idx = det_idx_2_title_idx[det_idx]
            old_title = title_line[old_title_idx]
            new_title = title_line[title_idx]

            old_distance = abs(old_title.rect.x1 - det_result.rect.x1)
            new_distance = abs(new_title.rect.x1 - det_result.rect.x1)

            if new_distance < old_distance:
                det_idx_2_title_idx[det_idx] = title_idx

    # 重新构建正向映射
    resolved_mapping = {}
    for det_idx, title_idx in det_idx_2_title_idx.items():
        resolved_mapping[title_idx] = det_idx

    return resolved_mapping


def _convert_line_to_goods_result(
        detect_line: list[AnalyseFileDetectResult],
        prop_list: list[AnalyseFileGoodsPropExample],
        prop_idx_2_title_idx: dict[int, int],
        title_idx_2_det_idx: dict[int, int]
) -> AnalyseFileGoodsResult:
    """
    将数据行转换为商品结果
    :param detect_line: 数据行
    :param prop_list: 属性列表
    :param prop_idx_2_title_idx: 属性索引到表头索引的映射
    :param title_idx_2_det_idx: 表头索引到识别结果索引的映射
    :return: 商品结果
    """
    result = AnalyseFileGoodsResult()

    for prop_idx, prop in enumerate(prop_list):
        # 找到属性对应的表头列
        title_idx = prop_idx_2_title_idx.get(prop_idx)
        if title_idx is None:
            continue

        # 找到表头列对应的识别结果列
        det_idx = title_idx_2_det_idx.get(title_idx)
        if det_idx is None:
            continue

        det_result = detect_line[det_idx]
        if det_result is None or not det_result.is_rect_valid:
            continue

        detect_text = det_result.rec_text
        result.prop_list.append(
            AnalyseFileGoodsResultProp(
                label=prop.prop_name,
                value='' if prop.prop_name == 'IMAGE' else detect_text,
                rect_str=det_result.rect.rect_text
            )
        )

    result.init_rect()

    return result


def _merge_incomplete_results_to_nearby(
        result_list: list[AnalyseFileGoodsResult],
) -> list[AnalyseFileGoodsResult]:
    """
    将属性较少的行合并到最近的属性较多的行上
    :param result_list: 商品结果列表
    :return: 合并后的结果列表
    """
    if len(result_list) == 0:
        return result_list

    max_prop_cnt: int = 0
    for result in result_list:
        if result.prop_cnt > max_prop_cnt:
            max_prop_cnt = result.prop_cnt

    # 定义完整行和不完整行的阈值
    completeness_threshold = max_prop_cnt * 0.5
    # 计算行间距离的统计值，用于设定合并距离阈值
    max_merge_distance = _calculate_merge_distance_threshold(result_list)

    # 分离完整行和不完整行
    complete_results = []
    incomplete_results = []

    for i, result in enumerate(result_list):
        if result.prop_cnt >= completeness_threshold:
            complete_results.append((i, result))
        else:
            incomplete_results.append((i, result))

    # 如果没有不完整的行，直接返回完整行
    if len(incomplete_results) == 0:
        return [result for _, result in complete_results]

    # 如果没有完整的行，返回所有行（避免丢失数据）
    if len(complete_results) == 0:
        return result_list

    # 将不完整行合并到最近的完整行
    merged_results = [result for _, result in complete_results]
    unmerged_results = []  # 存储无法合并的不完整行

    for incomplete_idx, incomplete_result in incomplete_results:
        # 找到最近的完整行
        nearest_complete_idx, distance = _find_nearest_complete_result_with_pixel_distance(
            incomplete_result, complete_results
        )

        # 只有距离在阈值内才进行合并
        if nearest_complete_idx is not None and distance <= max_merge_distance:
            # 合并属性到最近的完整行
            _merge_properties_to_target(
                source=incomplete_result,
                target=merged_results[nearest_complete_idx]
            )
        else:
            # 距离过大，保留为独立的商品结果
            unmerged_results.append(incomplete_result)

    # 返回合并后的完整行 + 无法合并的不完整行
    return merged_results + unmerged_results


def _calculate_merge_distance_threshold(result_list: list[AnalyseFileGoodsResult]) -> float:
    """
    计算行间距离的统计值，用于设定合并距离阈值
    :param result_list: 商品结果列表
    :return: 合并距离阈值（像素）
    """
    if len(result_list) < 2:
        return 100.0  # 默认阈值

    # 计算相邻行之间的距离
    distances = []
    for i in range(len(result_list) - 1):
        current_result = result_list[i]
        next_result = result_list[i + 1]

        # 获取当前行和下一行的Y坐标范围
        current_y = current_result.rect.y2
        next_y = next_result.rect.y1

        # 计算行间距离（下一行顶部 - 当前行底部）
        distance = next_y - current_y  # next_y1 - current_y2
        if distance > 0:  # 只考虑正向距离
            distances.append(distance)

    if not distances:
        return 100.0  # 默认阈值

    # 计算中位数作为基准距离
    distances.sort()
    median_distance = distances[len(distances) // 2]

    # 设置阈值为中位数的1.5倍，允许一定的容差
    return median_distance * 1.5


def _find_nearest_complete_result_with_pixel_distance(
        incomplete_result: AnalyseFileGoodsResult,
        complete_results: list[tuple[int, AnalyseFileGoodsResult]],
) -> tuple[int | None, float]:
    """
    找到距离不完整行最近的完整行在merged_results中的索引，并返回像素距离
    :param incomplete_result: 不完整行结果
    :param complete_results: 完整行列表 (原始索引, 结果)
    :return: (在merged_results中的索引, 像素距离)
    """
    min_distance = float('inf')
    nearest_merged_idx = None

    for merged_idx, (complete_idx, complete_result) in enumerate(complete_results):
        # 计算两行之间的距离
        distance = _calculate_row_distance(incomplete_result.rect, complete_result.rect)

        if distance < min_distance:
            min_distance = distance
            nearest_merged_idx = merged_idx

    return nearest_merged_idx, min_distance


def _calculate_row_distance(rect1: Rect, rect2: Rect) -> float:
    """
    计算两行之间的距离
    Args:
        rect1: 行1
        rect2: 行2

    Returns:
        距离（像素）
    """
    y1_min, y1_max = rect1.y1, rect1.y2
    y2_min, y2_max = rect2.y1, rect2.y2

    # 如果两行有重叠，距离为0
    if not (y1_max < y2_min or y2_max < y1_min):
        return 0.0

    # 计算最短距离
    if y1_max < y2_min:
        # 第一行在第二行上方
        return y2_min - y1_max
    else:
        # 第一行在第二行下方
        return y1_min - y2_max


def _calculate_col_distance(rect1: Rect, rect2: Rect) -> float:
    """
    计算两列之间的距离
    Args:
        rect1: 列1
        rect2: 列2

    Returns:
        距离（像素）
    """
    x1_min, x1_max = rect1.x1, rect1.x2
    x2_min, x2_max = rect2.x1, rect2.x2

    # 如果两列有重叠，距离为0
    if not (x1_max < x2_min or x2_max < x1_min):
        return 0.0

    # 计算最短距离
    if x1_max < x2_min:
        # 第一列在第二列左方
        return x2_min - x1_max
    else:
        # 第一列在第二列右方
        return x1_min - x2_max


def _find_nearest_complete_result_with_distance(
        incomplete_idx: int,
        complete_results: list[tuple[int, AnalyseFileGoodsResult]]
) -> tuple[int, int]:
    """
    找到距离不完整行最近的完整行在merged_results中的索引，并返回距离
    :param incomplete_idx: 不完整行的原始索引
    :param complete_results: 完整行列表 (原始索引, 结果)
    :return: (在merged_results中的索引, 距离)
    """
    if not complete_results:
        return None, float('inf')

    min_distance = float('inf')
    nearest_merged_idx = 0

    for merged_idx, (complete_idx, _) in enumerate(complete_results):
        distance = abs(incomplete_idx - complete_idx)
        if distance < min_distance:
            min_distance = distance
            nearest_merged_idx = merged_idx

    return nearest_merged_idx, min_distance


def _find_nearest_complete_result(
        incomplete_idx: int,
        complete_results: list[tuple[int, AnalyseFileGoodsResult]]
) -> int:
    """
    找到距离不完整行最近的完整行在merged_results中的索引
    :param incomplete_idx: 不完整行的原始索引
    :param complete_results: 完整行列表 (原始索引, 结果)
    :return: 在merged_results中的索引
    """
    nearest_idx, _ = _find_nearest_complete_result_with_distance(incomplete_idx, complete_results)
    return nearest_idx


def _merge_properties_to_target(
        source: AnalyseFileGoodsResult,
        target: AnalyseFileGoodsResult
) -> None:
    """
    将源商品的属性合并到目标商品中
    :param source: 源商品结果（属性较少的行）
    :param target: 目标商品结果（属性较多的行）
    """
    # 判断哪一行在上方
    if source.rect.y1 < target.rect.y1:
        # 源在上，目标在下，需要将源的属性值插入到目标属性值前
        append_mode = False
    else:
        # 源在下，目标在上，需要将源的属性值追加到目标属性值
        append_mode = True

    # 创建目标商品属性的映射，便于查找
    target_prop_map = {prop.label: prop for prop in target.prop_list}

    for source_prop in source.prop_list:
        if source_prop.label in target_prop_map:
            # 如果目标已有该属性，将源属性值追加到目标属性值
            target_prop = target_prop_map[source_prop.label]
            if source_prop.value and source_prop.value.strip():
                if target_prop.value and target_prop.value.strip():
                    # 使用换行符连接文本
                    if append_mode:
                        target_prop.value = target_prop.value + '\n' + source_prop.value
                    else:
                        target_prop.value = source_prop.value + '\n' + target_prop.value
                else:
                    # 目标属性值为空，直接使用源属性值
                    target_prop.value = source_prop.value
                    target_prop.rect_str = source_prop.rect_str
        else:
            # 如果目标没有该属性，直接添加
            if source_prop.value and source_prop.value.strip():
                target.prop_list.append(AnalyseFileGoodsResultProp(
                    label=source_prop.label,
                    value=source_prop.value,
                    rect_str=source_prop.rect_str
                ))

    # 合并后 重新计算矩形区域
    target.init_rect()


def _filter_incomplete_results(
        result_list: list[AnalyseFileGoodsResult],
) -> list[AnalyseFileGoodsResult]:
    """
    过滤掉属性数量过少的行
    Args:
        result_list: 商品结果列表

    Returns:
        过滤后的商品结果列表
    """
    max_prop_cnt = 0
    for result in result_list:
        if result.prop_cnt > max_prop_cnt:
            max_prop_cnt = result.prop_cnt

    min_prop_cnt = max_prop_cnt * 0.5
    return [result for result in result_list if result.prop_cnt >= min_prop_cnt]
