import os

from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.basic.point import Point
from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResult, \
    AnalyseFileGoodsResultProp
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.context import AppContext
from op_purchase_ai.utils import basic_utils
from op_purchase_ai.utils import cv2_utils


class AnalyseFileGroupWrapper:

    def __init__(
            self,
            image_file_path: str,
            group_value_list: list[str],
            group_rect_list: list[Rect],
    ):
        self.image_file_path: str = image_file_path
        self.group_value_list: list[str] = group_value_list
        self.group_rect_list: list[Rect] = group_rect_list


class AnalyseFileGoodsService:

    def __init__(self, ctx: AppContext):
        self.ctx: AppContext = ctx

    def extract_goods_info(
            self,
            task_type: str,
            image_file_path: str,
            prop_list: list[AnalyseFileGoodsPropExample],
            group_prop_list: list[AnalyseFileGoodsPropExample],
    ) -> list[AnalyseFileGoodsResult]:
        """
        提取商品信息

        Args:
            task_type: 图片类型
            image_file_path: 本地图片路径
            prop_list: 商品属性列表
            group_prop_list: 商品分组列表

        Returns:
            list[AnalyseFileGoodsResult]: 商品结果列表
        """
        return self.ctx.analyse_file_dify_service.goods_info_extract(
            task_type=task_type,
            image_file_path=image_file_path,
            prop_list=prop_list,
        )


    def extract_goods_info_by_group(
            self,
            task_type: str,
            image_file_path: str,
            prop_list: list[AnalyseFileGoodsPropExample],
            group_prop_list: list[AnalyseFileGoodsPropExample],
    ) -> list[AnalyseFileGoodsResult]:
        """
        提取商品信息

        Args:
            task_type: 图片类型
            image_file_path: 本地图片路径
            prop_list: 商品属性列表
            group_prop_list: 商品分组列表

        Returns:
            list[AnalyseFileGoodsResult]: 商品结果列表
        """
        temp_file_path_list: list[str] = []
        try:
            group_list: list[AnalyseFileGroupWrapper] = [AnalyseFileGroupWrapper(
                image_file_path=image_file_path,
                group_value_list=[],
                group_rect_list=[],
            )]

            for group_prop in group_prop_list:
                next_group_list: list[AnalyseFileGroupWrapper] = []
                for group in group_list:
                    sub_group_result_list: list[AnalyseFileGoodsResultProp] = self.ctx.analyse_file_dify_service.goods_info_crop_by_group(
                        image_file_path=group.image_file_path,
                        group_prop=group_prop,
                    )

                    current_image: MatLike = cv2_utils.read_image(group.image_file_path)

                    # 没有分组的情况 整块继续传递下去
                    if len(sub_group_result_list) == 0:
                        next_group_list.append(AnalyseFileGroupWrapper(
                            image_file_path=group.image_file_path,
                            group_value_list=group.group_value_list + [''],
                            group_rect_list=group.group_rect_list + [Rect(0, 0, current_image.shape[1], current_image.shape[0])],
                        ))
                        continue

                    for sub_group_idx, sub_group_result in enumerate(sub_group_result_list):
                        rect: Rect = basic_utils.rect_from_str(sub_group_result.rect_str)
                        sub_image = cv2_utils.crop_image_only(current_image, rect)

                        # 将切割后的图片保存为新的临时文件
                        sub_image_path = os.path.join(
                            os.path.dirname(group.image_file_path),
                            f'{os.path.splitext(os.path.basename(group.image_file_path))[0]}_{sub_group_idx}.png'
                        )
                        cv2_utils.save_image(sub_image, sub_image_path)
                        temp_file_path_list.append(sub_image_path)

                        next_group_list.append(AnalyseFileGroupWrapper(
                            image_file_path=sub_image_path,
                            group_value_list=group.group_value_list + [sub_group_result.value],
                            group_rect_list=group.group_rect_list + [rect],
                        ))

                group_list = next_group_list

            goods_result_list: list[AnalyseFileGoodsResult] = []
            for group in group_list:
                group_goods_result_list: list[AnalyseFileGoodsResult] = self.ctx.analyse_file_dify_service.goods_info_extract(
                    task_type=task_type,
                    image_file_path=group.image_file_path,
                    prop_list=prop_list,
                )

                for goods_result in group_goods_result_list:
                    # 分组裁剪的坐标偏移量
                    group_left_top: Point = Point(0, 0)
                    for group_rect in group.group_rect_list:
                        group_left_top = group_left_top + group_rect.left_top

                    # 对识别结果进行偏移
                    for goods_prop in goods_result.prop_list:
                        rect: Rect = basic_utils.rect_from_str(goods_prop.rect_str)
                        rect.add_offset(group_left_top)
                        goods_prop.rect_str = rect.rect_text

                    # 添加分组信息
                    group_prop_result_list: list[AnalyseFileGoodsResultProp] = []
                    for group_prop_idx in range(len(group_prop_list)):
                        group_prop_result_list.append(AnalyseFileGoodsResultProp(
                            label=f'Group {group_prop_list[group_prop_idx].prop_name}',
                            value=group.group_value_list[group_prop_idx],
                            rect_str='[0, 0, 0, 0]',
                        ))

                    # 合并属性
                    goods_result.prop_list = group_prop_result_list + goods_result.prop_list
                    goods_result_list.append(goods_result)

            return goods_result_list
        finally:
            self.ctx.remove_temp_files(temp_file_path_list)
