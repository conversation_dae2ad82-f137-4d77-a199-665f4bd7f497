from typing import Optional

from pydantic import BaseModel, Field

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.utils import basic_utils


class AnalyseFileGoodsResultProp(BaseModel):

    label: str = Field(default='', description='属性名称')
    value: str = Field(default='', description='属性值')
    rect_str: str = Field(default='[]', description='属性所在区域的bbox坐标 字符串')


class AnalyseFileGoodsResult(BaseModel):

    rect: Optional[Rect] = Field(default=None, description='商品区域')

    file_name: str = Field(default='', description='文件名称')
    file_page: int = Field(default=0, description='文件页数 从1开始')
    goods_idx: int = Field(default=0, description='商品下标')

    prop_list: list[AnalyseFileGoodsResultProp] = Field(default=[], description='商品属性列表')

    @property
    def prop_cnt(self) -> int:
        """
        :return: 当前拥有的属性数量
        """
        return len(self.prop_list)

    def init_rect(self) -> None:
        """
        初始化商品区域
        :return:
        """
        rect_list: list[Rect] = []
        for prop in self.prop_list:
            rect = basic_utils.rect_from_str(prop.rect_str)
            if rect is not None and rect.is_valid:
                rect_list.append(rect)

        if rect_list:
            self.rect = basic_utils.merge_rect_list(rect_list)
