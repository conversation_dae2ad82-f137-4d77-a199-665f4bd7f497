from typing import Optional

from pydantic import BaseModel, Field

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.utils import basic_utils


class AnalyseFileGoodsPropExample(BaseModel):

    prop_name: str = Field(..., description='属性')
    prop_name_label: str = Field(..., description='属性-名称')
    prop_type: str = Field(..., description='属性类型')
    prop_rect: str = Field(default='[0, 0, 0, 0]', description='示例属性的区域矩形坐标')
    prop_text: str = Field(default='', description='示例属性的文本')

    rect: Optional[Rect] = Field(default=None, description='示例属性的区域矩形 应该在请求入口初始化')

    @property
    def is_rect_valid(self) -> bool:
        return self.rect is not None and self.rect.is_valid

    def init_rect(self):
        self.rect = basic_utils.rect_from_str(self.prop_rect)
