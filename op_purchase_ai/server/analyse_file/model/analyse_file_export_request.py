from pydantic import BaseModel, Field

from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResult
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.response.common_response import CommonResponse


class AnalyseResultExportRequest(BaseModel):

    file_name: str = Field(..., description='原始文件名称')
    page_fid_map: dict[str, str] = Field(..., description='每一页的fid vfs系统的文件ID')
    prop_list: list[AnalyseFileGoodsPropExample] = Field(..., description='商品属性列表')
    goods_result_list: list[AnalyseFileGoodsResult] = Field(..., description='商品结果列表')


class AnalyseResultExportResponse(CommonResponse):

    file_name: str = Field(default='', description='导出的文件名')
    fid: str = Field(default='', description='vfs系统的文件ID')