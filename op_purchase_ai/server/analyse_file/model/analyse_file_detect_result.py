from typing import Optional

from pydantic import BaseModel, Field

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.utils import basic_utils


class AnalyseFileDetectResult(BaseModel):

    file_name: str = Field(default='', description='文件名称')
    file_page: int = Field(default=0, description='文件页数')

    det_idx: int = Field(..., description='识别结果的下标')
    det_type: str = Field(..., description='识别方式')
    det_rect: str = Field(..., description='识别结果的区域矩形坐标')
    rec_type: str = Field(..., description='识别结果的类型')
    rec_text: str = Field(..., description='识别结果的文本')

    rect: Optional[Rect] = Field(default=None, description='识别结果的区域矩形 由det_rect转化 请求入口处应该做好初始化')

    @property
    def is_rect_valid(self) -> bool:
        return self.rect is not None and self.rect.is_valid

    def init_rect(self) -> None:
        self.rect = basic_utils.rect_from_str(self.det_rect)
