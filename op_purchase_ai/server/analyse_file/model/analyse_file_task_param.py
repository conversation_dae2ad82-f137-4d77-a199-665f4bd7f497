from pydantic import BaseModel, Field


class AnalyseFileTaskParam(BaseModel):

    task_type: str = Field(default='', description='任务类型')
    page_start: int = Field(default=1, description='开始页码')
    page_end: int = Field(default=9999, description='结束页码')
    user_provide_example: bool = Field(default=False, description='用户是否提供了属性示例')
    page_invoice_num: bool = Field(default=False, description='是否需要按页提取发票号')
