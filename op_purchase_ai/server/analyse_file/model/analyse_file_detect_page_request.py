from pydantic import BaseModel, Field

from op_purchase_ai.server.analyse_file.model.analyse_file_detect_result import AnalyseFileDetectResult
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.response.common_response import CommonResponse


class AnalyseFileDetectPageRequest(BaseModel):

    task_type: str = Field(default='', description='任务类型')
    original_fid: str = Field(default='', description='原始文件的vfs系统的文件ID')
    page_idx: int = Field(default=0, description='要识别的页面的下标 从1开始')
    page_fid: str = Field(default='', description='要识别的页面的vfs系统的文件ID')

    prop_example_list: list[AnalyseFileGoodsPropExample] = Field(default=[], description='属性示例列表')


class AnalyseFileDetectPageResponse(CommonResponse):

    result_list: list[AnalyseFileDetectResult] = Field(default=[], description="文本和图片识别结果")
