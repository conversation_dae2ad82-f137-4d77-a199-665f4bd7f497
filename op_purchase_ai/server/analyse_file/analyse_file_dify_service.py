import os
import time

from op_purchase_ai.dify.dify_workflow_client import DifyWorkflowClient
from op_purchase_ai.dify.models.base import FileItem, FileType, ResponseMode, TransferMethod
from op_purchase_ai.dify.models.workflow_run import WorkflowRunCompletionResponse, WorkflowRunRequest
from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResult, \
    AnalyseFileGoodsResultProp
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.server.api.model.analyse_file.anaylse_file_goods_group import \
    AnalyseFilePredictGoodsGroupDifyResult, AnalyseFilePredictGoodsGroupNode
from op_purchase_ai.server.service.op_purchase_dify_service import OpPurchaseDifyService
from op_purchase_ai.utils import env_utils, os_utils
from op_purchase_ai.utils.log_utils import log


class AnalyseFileDifyService(OpPurchaseDifyService):

    def __init__(
            self,
            base_url: str
        ) -> None:
        OpPurchaseDifyService.__init__(self, base_url=base_url)

    def goods_info_predict_group(
            self,
            image_file_path: str,
    ) -> tuple[bool, list[AnalyseFilePredictGoodsGroupDifyResult]]:
        op = '文件分析 商品分组预测'
        client = DifyWorkflowClient(
            base_url=self.base_url,
            api_key=env_utils.get_str('DIFY_GOODS_INFO_PREDICT_GROUP_API_KEY'),
        )

        request = WorkflowRunRequest(
            inputs={
                'goods_image': FileItem(
                    transfer_method=TransferMethod.LOCAL_FILE,
                    type=FileType.IMAGE,
                    upload_file_path=image_file_path
                ),
            },
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )
        log.info(f'[{op}] 入参: {request.model_dump_json()}')
        response: WorkflowRunCompletionResponse = client.run(
            request=request
        )
        log.info(f'[{op}] 结果: {response.model_dump_json()}')
        is_goods_page = response.data.outputs.get('is_goods_page', False) # type: ignore
        json_list = response.data.outputs.get('title_list', []) # type: ignore
        return is_goods_page, [AnalyseFilePredictGoodsGroupDifyResult.model_validate(json) for json in json_list]


    def goods_info_summarize_group(
            self,
            image_file_path: str,
            group_root: AnalyseFilePredictGoodsGroupNode,
    ) -> list[AnalyseFileGoodsPropExample]:
        op = '文件分析 商品分组总结'
        client = DifyWorkflowClient(
            base_url=self.base_url,
            api_key=env_utils.get_str('DIFY_GOODS_INFO_SUMMARIZE_GROUP_API_KEY'),
        )

        request = WorkflowRunRequest(
            inputs={
                'goods_image': FileItem(
                    transfer_method=TransferMethod.LOCAL_FILE,
                    type=FileType.IMAGE,
                    upload_file_path=image_file_path
                ),
                'group_json_str': group_root.model_dump_json(exclude={'file_path'})
            },
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )
        log.info(f'[{op}] 入参: {request.model_dump_json()}')
        response: WorkflowRunCompletionResponse = client.run(
            request=request
        )
        log.info(f'[{op}] 结果: {response.model_dump_json()}')
        json_list = response.data.outputs.get('title_list', []) # type: ignore

        return [
            AnalyseFileGoodsPropExample(
                prop_name=i.get('group_type'),
                prop_name_label=i.get('group_type_label'),
                prop_type='GROUP',
                prop_rect='[]',
                prop_text=i.get('group_title'),
                rect=None,
            )
            for i in json_list
        ]

    def goods_info_crop_by_group(
            self,
            image_file_path: str,
            group_prop: AnalyseFileGoodsPropExample,
    ) -> list[AnalyseFileGoodsResultProp]:
        """
        按给定的分组方式 对图片进行划分

        Args:
            image_file_path: 图片地址
            group_prop: 分组属性

        Returns:
            list[AnalyseFileGoodsResultProp]: 划分后的图片列表
        """
        op = '文件分析 商品分组裁剪'
        client = DifyWorkflowClient(
            base_url=self.base_url,
            api_key=env_utils.get_str('DIFY_GOODS_INFO_CROP_BY_GROUP_API_KEY'),
        )

        request = WorkflowRunRequest(
            inputs={
                'goods_image': FileItem(
                    transfer_method=TransferMethod.LOCAL_FILE,
                    type=FileType.IMAGE,
                    upload_file_path=image_file_path
                ),
                'group_json_str': group_prop.model_dump_json()
            },
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )

        log.info(f'[{op}] 入参: {request.model_dump_json()}')
        response: WorkflowRunCompletionResponse = client.run(
            request=request
        )
        log.info(f'[{op}] 结果: {response.model_dump_json()}')
        json_list = response.data.outputs.get('group_list', []) # type: ignore
        return [AnalyseFileGoodsResultProp.model_validate(json) for json in json_list]


    def goods_info_predict_example(
            self,
            image_file_type: str,
            image_file_path: str,
    ) -> list[AnalyseFileGoodsPropExample]:
        op = '文件分析 商品属性预测'
        client = DifyWorkflowClient(
            base_url=self.base_url,
            api_key=env_utils.get_str('DIFY_GOODS_INFO_EXAMPLE_API_KEY'),
        )

        request = WorkflowRunRequest(
            inputs={
                'goods_image': FileItem(
                    transfer_method=TransferMethod.LOCAL_FILE,
                    type=FileType.IMAGE,
                    upload_file_path=image_file_path
                ),
                'image_type': image_file_type,
            },
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )

        total_cnt = 10
        for idx in range(total_cnt):
            log.info(f'[{op}] 入参: {request.model_dump_json()}')
            response: WorkflowRunCompletionResponse = client.run(
                request=request
            )
            log.info(f'[{op}] 结果: {response.model_dump_json()}')

            if response.data.status != 'succeeded':
                # 出现错误 重试一次
                log.warning(f'[{op}] 运行出错 当前 {idx+1}/{total_cnt} 次')
                if response.data.error is not None and response.data.error.find('429') != -1:
                    log.warning(f'[{op}] 大模型限流 当前 {idx + 1}/{total_cnt} 次')
                    time.sleep(10)  # 触发了限流 等待一段时间后再继续
                if idx == total_cnt - 1:
                    raise Exception('请求Dify失败')
                continue
            else:
                json_list = response.data.outputs.get('prop_list', []) # type: ignore
                prop_list = [AnalyseFileGoodsPropExample.model_validate(json) for json in json_list]
                wrong_result: bool = False
                for prop in prop_list:
                    prop.init_rect()
                    if prop.rect is None or not prop.rect.is_valid:
                        wrong_result = True
                        break

                if wrong_result:
                    log.warning(f'[{op}] 返回结果不通过校验 当前 {idx + 1}/{total_cnt} 次')
                    continue
                else:
                    return prop_list

        raise f'[{op}] 失败'

    def goods_info_extract(
            self,
            task_type: str,
            image_file_path: str,
            prop_list: list[AnalyseFileGoodsPropExample],
    ) -> list[AnalyseFileGoodsResult]:
        op = '文件分析 商品信息提取'

        request = WorkflowRunRequest(
            inputs={
                'goods_image': FileItem(
                    transfer_method=TransferMethod.LOCAL_FILE,
                    type=FileType.IMAGE,
                    upload_file_path=image_file_path
                ),
                'image_type': task_type,
                'prop_list_str': "[" + ", ".join(p.model_dump_json() for p in prop_list) + "]"
            },
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )

        response: WorkflowRunCompletionResponse = self.run_workflow(
            run_name=op,
            api_key=env_utils.get_str('DIFY_GOODS_INFO_EXTRACT_API_KEY'),
            request=request,
        )
        json_list = response.data.outputs.get('goods_list', [])  # type: ignore

        result_list: list[AnalyseFileGoodsResult] = []
        for idx, json in enumerate(json_list):
            result_list.append(AnalyseFileGoodsResult(
                goods_idx=idx,
                prop_list=json.get('prop_list', []),
            ))
        return result_list

    def extract_invoice_num(
            self,
            image_file_path: str,
    ) -> str:
        """
        提取图片中的发票号

        Args:
            image_file_path: 图片文件路径

        Returns:
            str: 发票号
        """
        op = '文件分析 提取发票号'

        request = WorkflowRunRequest(
            inputs={
                'image': FileItem(
                    transfer_method=TransferMethod.LOCAL_FILE,
                    type=FileType.IMAGE,
                    upload_file_path=image_file_path
                ),
            },
            response_mode=ResponseMode.BLOCKING,
            user=self.user
        )

        response: WorkflowRunCompletionResponse = self.run_workflow(
            run_name=op,
            api_key=env_utils.get_str('DIFY_EXTRACT_INVOICE_NUM_API_KEY'),
            request=request,
        )
        return response.data.outputs.get('invoice_number', '')  # type: ignore


def __debug_goods_info_predict_example():
    service = AnalyseFileDifyService(
        base_url=env_utils.get_str('DIFY_API_BASE_URL'),
    )
    test_file_path = os.path.join(
        os_utils.get_path_under_work_dir([".temp", "pdf2images", "hh"]),
        "hh_2.png"
        )
    result_list = service.goods_info_predict_example(
        image_file_type='货单类',
        image_file_path=test_file_path,
    )
    print(result_list)


def __debug_goods_info_extract():
    service = AnalyseFileDifyService(
        base_url=env_utils.get_str('DIFY_API_BASE_URL'),
    )
    test_file_path = os.path.join(
        os_utils.get_path_under_work_dir([".temp", "pdf2images", "hh"]),
        "hh_2.png"
        )
    prop_list=[
        {
            "prop_name": "sizes_available",
            "prop_name_label": "尺寸可用",
            "prop_type": "TEXT",
            "prop_text": "40/7, 40.5/7.5, 41/8, 42/8.5, 42.5/9, 43/9.5, 44/10, 44.5/10.5, 45/11, 46/11.5, 46.5/12, 48/13",
            "prop_rect": "[28,124,167,168]"
        },
        {
            "prop_name": "features",
            "prop_name_label": "特性",
            "prop_type": "TEXT",
            "prop_text": "HH® Quick Dry, HH® Max-Vent, Flowknit®, HH® Pro Guard, HH® Hover-Stride, HH® Max-Comfort Insole, HH® Free-Flex, HH Surround-Grip, HH Hydro-Grip, HH Storm Siping, Serdia Clarino (UV+ Activated Antibacterial Technology), 100% Recycled Lace, 100% Recycled Polyester Webbing, 100% RPET Reinforcement Tape and Collar Reinforcement, 20% Recycled Rubber, 100% Recycled Lining, 30% Recycled EVA Mid-sole",
            "prop_rect": "[200,113,432,368]"
        },
        {
            "prop_name": "goods_image",
            "prop_name_label": "商品图片",
            "prop_type": "IMAGE",
            "prop_text": "",
            "prop_rect": "[498,64,777,368]"
        }
    ]

    result_list = service.goods_info_extract(
        task_type='货单类',
        image_file_path=test_file_path,
        prop_list=[AnalyseFileGoodsPropExample.model_validate(p) for p in prop_list]
    )
    for result in result_list:
        print(result.model_dump_json())


if __name__ == '__main__':
    __debug_goods_info_predict_example()
    # __debug_goods_info_extract()
