import io
import json
import os
from typing import Optional, Any

from PIL import Image as PILImage
from cv2.typing import Mat<PERSON><PERSON>
from openpyxl.drawing.image import Image
from openpyxl.workbook import Workbook
from openpyxl.worksheet.worksheet import Worksheet

from op_purchase_ai.basic.rectangle import Rect
from op_purchase_ai.server.analyse_file import analyse_file_const
from op_purchase_ai.server.analyse_file.model.analyse_file_detect_result import AnalyseFileDetectResult
from op_purchase_ai.server.analyse_file.model.analyse_file_goods_result import AnalyseFileGoodsResult, \
    AnalyseFileGoodsResultProp
from op_purchase_ai.server.analyse_file.model.analyse_file_prop_example import AnalyseFileGoodsPropExample
from op_purchase_ai.utils import cv2_utils, os_utils, cal_utils, basic_utils, detect_result_utils
from op_purchase_ai.utils.log_utils import log
from op_purchase_ai.yolo.yolo_match_result import Yolo<PERSON>atchResult, DetectClass


class AnalyseFileService:

    def __init__(
            self,
    ):
        pass

    def predict_image(
            self,
            task_type: str,
            page_image: MatLike,
            prop_list: list[AnalyseFileGoodsPropExample],
            detect_result_list: list[AnalyseFileDetectResult],
    ) -> list[YoloMatchResult]:
        with_image_prop: bool = False  # 是否存在图片属性
        for prop in prop_list:
            if prop.prop_type == 'IMAGE':
                with_image_prop = True

        if not with_image_prop:
            return []
        elif task_type == 'TABLE':
            return self.predict_image_for_table(page_image, prop_list, detect_result_list)
        else:
            return []

    def predict_image_for_table(
            self,
            page_image: MatLike,
            prop_list: list[AnalyseFileGoodsPropExample],
            detect_result_list: list[AnalyseFileDetectResult],
    ) -> list[YoloMatchResult]:
        """
        针对表格类
        在每一行 使用图片示例的横坐标 自动计算该行的图片矩形区域
        :param page_image: 分页图片
        :param prop_list: 属性示例
        :param detect_result_list: 识别结果
        :return:
        """
        match_result_list: list[YoloMatchResult] = []

        # 合并多行结果 注意行内一定要按横坐标排序
        line_list = detect_result_utils.merge_to_lines(detect_result_list)

        # 找到表头行
        title_line = detect_result_utils.find_title_line(line_list, prop_list)
        title_rect: Rect = basic_utils.merge_rect_list([i.rect for i in title_line])
        if title_rect is None or not title_rect.is_valid:
            raise Exception('表头行区域异常')

        # 找到属性对应的表头
        prop_idx_2_title_idx: dict[int, int] = detect_result_utils.get_prop_idx_2_title_idx(title_line, prop_list)

        # 根据表头 找到图片列的横坐标
        image_x1: int = -1
        image_x2: int = -1
        for prop_idx, prop in enumerate(prop_list):
            if prop.prop_type != 'IMAGE':
                continue

            title_idx: int = prop_idx_2_title_idx.get(prop_idx)
            if title_idx is None:
                continue

            title: AnalyseFileDetectResult = title_line[title_idx]
            if not title.is_rect_valid:
                continue

            image_x1 = title.rect.x1
            image_x2 = title.rect.x2

            # 截图的话 从本列的开始 截取到下一列的开始
            if title_idx + 1 < len(title_line):
                next_title: AnalyseFileDetectResult = title_line[title_idx + 1]
                image_x2 = next_title.rect.x1

        if image_x1 == -1 or image_x2 == -1:
            # 没有图片列
            return match_result_list

        # 逐行 取出图片区域
        for line_idx, line in enumerate(line_list):
            current_rect: Rect = basic_utils.merge_rect_list([i.rect for i in line])
            if current_rect is None or not current_rect.is_valid:
                log.error('识别结果行出现非法的矩形区域')
                continue

            if current_rect.center.y < title_rect.y2:  # 过滤表头行上方的
                continue

            if line_idx == 0:
                last_rect: Optional[Rect] = None
            else:
                last_rect: Rect = basic_utils.merge_rect_list([i.rect for i in line_list[line_idx - 1]])

            if line_idx + 1 >= len(line_list):
                next_rect: Optional[Rect] = None
            else:
                next_rect: Rect = basic_utils.merge_rect_list([i.rect for i in line_list[line_idx + 1]])

            line_y1: int = current_rect.y1
            line_y2: int = current_rect.y2

            # 纵坐标上 上下两行平分中间的空隙
            if last_rect is not None:
                line_y1 = last_rect.y2 + (current_rect.y1 - last_rect.y2) // 2
            if next_rect is not None:
                line_y2 = current_rect.y2 + (next_rect.y1 - current_rect.y2) // 2

            match_result_list.append(
                YoloMatchResult(
                    confidence=1.0,
                    x=int(image_x1),
                    y=int(line_y1),
                    w=int(image_x2 - image_x1),
                    h=int(line_y2 - line_y1),
                    template_scale=1.0,
                    data=DetectClass(class_id=-1, class_name='unknown', class_category='unknown')
                )
            )

        # 过滤本来已经识别到的图片
        filter_match_result_list: list[YoloMatchResult] = []
        for match_result in match_result_list:
            existed: bool = False
            for image_result in detect_result_list:
                if image_result.rec_type != 'IMAGE':
                    continue
                if cal_utils.max_overlap_percent(image_result.rect, match_result.rect) > 0.8:
                    existed = True
                    break
            if not existed:
                filter_match_result_list.append(match_result)

        return filter_match_result_list

    def export_goods_result(
            self,
            file_name: str,
            page_2_image: dict[int, MatLike],
            prop_list: list[AnalyseFileGoodsPropExample],
            goods_result_list: list[AnalyseFileGoodsResult]
    ) -> str:
        """
        导出商品结果
        :param file_name: 原始文件名称
        :param page_2_image: 页码对应的图片
        :param prop_list: 商品属性列表
        :param goods_result_list: 商品结果列表
        :return: 导出文件的filepath
        """
        # 创建 Excel 工作簿和工作表
        wb = Workbook()
        ws: Worksheet = wb.active  # type: ignore

        # 添加几个PDF属性字段
        prop_list.append(AnalyseFileGoodsPropExample(
            prop_name=analyse_file_const.PROP_NAME_PDF_FILE_NAME,
            prop_name_label=analyse_file_const.PROP_NAME_PDF_FILE_NAME,
            prop_text='',
            prop_rect='',
            prop_type='TEXT'
        ))
        prop_list.append(AnalyseFileGoodsPropExample(
            prop_name=analyse_file_const.PROP_NAME_PDF_PAGE_NUM,
            prop_name_label=analyse_file_const.PROP_NAME_PDF_PAGE_NUM,
            prop_text='',
            prop_rect='',
            prop_type='TEXT'
        ))
        prop_list.append(AnalyseFileGoodsPropExample(
            prop_name=analyse_file_const.PROP_NAME_PDF_INVOICE_NUMBER,
            prop_name_label=analyse_file_const.PROP_NAME_PDF_INVOICE_NUMBER,
            prop_text='',
            prop_rect='',
            prop_type='TEXT'
        ))

        # 设置表头
        header_id_list = [i.prop_name for i in prop_list]

        for col_num, header in enumerate(header_id_list, 1):
            ws.cell(row=1, column=col_num, value=header)

        goods_result_list.sort(key=lambda x: (x.file_name, x.file_page, x.goods_idx))

        prop_name_2_def: dict[str, AnalyseFileGoodsPropExample] = {}
        for prop in prop_list:
            prop_name_2_def[prop.prop_name] = prop
        
        expand_row_height: dict[int, int] = {}
        expand_col_width: dict[int, int] = {}

        # 填充数据
        for row_num, goods_result in enumerate(goods_result_list, 2):  # 从第 2 行开始填充数据
            file_name = goods_result.file_name
            file_page = goods_result.file_page
            page_image = page_2_image[file_page]
            row_data: dict[str, Any] = self.convert_excel_row(page_image, prop_list, goods_result)
            # 赋值额外的PDF属性
            row_data[analyse_file_const.PROP_NAME_PDF_FILE_NAME] = file_name
            row_data[analyse_file_const.PROP_NAME_PDF_PAGE_NUM] = file_page

            for col_num, col_name in enumerate(header_id_list, 1):
                prop_def: AnalyseFileGoodsPropExample | None = prop_name_2_def.get(col_name)
                if prop_def is None:
                    continue

                if prop_def.prop_type == 'IMAGE':  # TODO 导出图片时 需要让图片的anchor完全落在一个单元格上 避免后续读取问题
                    img = row_data.get(prop_def.prop_name)
                    if img is None:
                        continue
                    # 插入 opencv 格式的图片
                    pil_img = PILImage.fromarray(img)

                    # 将 PIL 图像保存为字节流
                    img_byte_arr = io.BytesIO()
                    pil_img.save(img_byte_arr, format='PNG')
                    img_byte_arr.seek(0)

                    # 创建 openpyxl 的 Image 对象
                    img = Image(img_byte_arr)

                    # 插入图片到 Excel
                    ws.add_image(img, f'{chr(col_num + 64)}{row_num}')  # 将图片插入到 A 列对应行

                    # 设置单元格后 再判断是否需要拓展行高度和列宽度
                    if row_num not in expand_row_height or img.height > expand_row_height[row_num]:
                        expand_row_height[row_num] = img.height
                    if col_num not in expand_col_width or img.width > expand_col_width[col_num]:
                        expand_col_width[col_num] = img.width
                else:
                    text = row_data.get(prop_def.prop_name, '')
                    ws.cell(row=row_num, column=col_num, value=text)

        # 拓展表格的高宽
        for row_num, height in expand_row_height.items():
            ws.row_dimensions[row_num].height = height + 10
        for col_num, width in expand_col_width.items():
            # Excel宽度是设置字符数量 假设字符宽度系数为 6px/字符
            ws.column_dimensions[chr(col_num + 64)].width = (width + 10) // 6

        original_file_name = file_name[:file_name.rfind('.')]
        temp_filepath = os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'export']),
            f'{original_file_name}.xlsx'
        )
        wb.save(temp_filepath)

        return temp_filepath

    def convert_excel_row(
            self,
            page_image: MatLike,
            prop_list: list[AnalyseFileGoodsPropExample],
            goods_result: AnalyseFileGoodsResult
    ) -> dict[str, Any]:
        """
        将商品结果转换成Excel行
        :param page_image: 分页图片
        :param prop_list: 商品属性列表
        :param goods_result: 商品结果
        :return: Excel行
        """
        prop_2_value: dict[str, AnalyseFileGoodsResultProp] = {}
        for goods_prop in goods_result.prop_list:
            prop_2_value[goods_prop.label] = goods_prop

        result: dict[str, Any] = {}
        for prop in prop_list:
            goods_prop = prop_2_value.get(prop.prop_name)
            if goods_prop is None:
                continue

            if prop.prop_type == 'TEXT':
                result[prop.prop_name] = goods_prop.value
            else:
                image = self.crop_image(page_image, goods_prop.rect_str)
                if image is not None:
                    result[prop.prop_name] = image

        return result

    def crop_image(
            self,
            image: MatLike,
            rect_str: str,
    ) -> MatLike | None:
        """
        截取所需图片
        :param image: 图片
        :param rect_str: 图片区域 [1, 2, 3, 4]
        :return: 提取的图片
        """
        rect_arr = json.loads(rect_str)
        rect = Rect(rect_arr[0], rect_arr[1], rect_arr[2], rect_arr[3])
        if rect.is_valid:
            return cv2_utils.crop_image_only(image, rect)
        else:
            return None
