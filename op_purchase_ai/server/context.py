import os

from op_purchase_ai.utils import env_utils


class AppContext:
    
    def __init__(self) -> None:
        from op_purchase_ai.server.service.vfs_service import VfsService
        self.vfs_service = VfsService(group='op_purchase_ai')

        from op_purchase_ai.server.service.ocr_service import OcrService
        self.ocr_service = OcrService(
            parallel=env_utils.get_int('OCR_MODEL_NUM', 1),
            wait_timeout_seconds=env_utils.get_int('OCR_WAIT_TIMEOUT_SECONDS', 5),
        )

        from op_purchase_ai.server.service.yolo_service import YoloService
        self.yolo_service = YoloService(
            model_name=env_utils.get_str('YOLO_MODEL', 'yolov8x-det'),
            parallel=env_utils.get_int('YOLO_MODEL_NUM', 1),
            wait_timeout_seconds=env_utils.get_int('YOLO_WAIT_TIMEOUT_SECONDS', 5)
        )

        from op_purchase_ai.server.service.pdf_service import PdfService
        self.pdf_service = PdfService(self)

        from op_purchase_ai.server.analyse_file.analyse_file_service import AnalyseFileService
        self.analyse_goods_service = AnalyseFileService()
        from op_purchase_ai.server.analyse_file.analyse_file_goods_service import AnalyseFileGoodsService
        self.analyse_file_goods_service = AnalyseFileGoodsService(self)
        from op_purchase_ai.server.analyse_file.analyse_file_table_service import AnalyseFileTableService
        self.analyse_file_table_service = AnalyseFileTableService(self)

        from op_purchase_ai.server.service.crawl_service import CrawlService
        self.crawl_service = CrawlService()

        from op_purchase_ai.server.analyse_file.analyse_file_dify_service import AnalyseFileDifyService
        self.analyse_file_dify_service = AnalyseFileDifyService(
            base_url=env_utils.get_str('DIFY_API_BASE_URL', 'http://ai-agent.api.vip.com/v1'),
        )

        from op_purchase_ai.server.lookup.lookup_service import LookupService
        self.lookup_service = LookupService(self)

    @staticmethod
    def remove_temp_files(file_path_list: list[str]) -> None:
        """
        删除临时文件
        Args:
            file_path_list: 文件路径列表

        Returns:
            None
        """
        for file_path in file_path_list:
            if os.path.exists(file_path):
                os.remove(file_path)


app_context = AppContext()
