from contextlib import asynccontextmanager

from fastapi import FastAPI, Response

from op_purchase_ai.server.api.analyse_file_api import router as analyse_file_router
from op_purchase_ai.server.api.goods_info_api import router as goods_info_router
from op_purchase_ai.server.api.image_api import router as image_router
from op_purchase_ai.server.api.lookup_api import router as lookup_router
from op_purchase_ai.server.api.pdf_api import router as pdf_router
from op_purchase_ai.server.context import app_context
from op_purchase_ai.utils import thread_utils


@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    服务初始化
    :return:
    """
    f1 = thread_utils.async_init_executor.submit(app_context.ocr_service.init_model)
    thread_utils.handle_future_result(f1)

    f2 = thread_utils.async_init_executor.submit(app_context.yolo_service.init_model)
    thread_utils.handle_future_result(f2)
    yield


app = FastAPI(lifespan=lifespan)
app.include_router(router=image_router)
app.include_router(router=pdf_router)
app.include_router(router=analyse_file_router)
app.include_router(router=goods_info_router)
app.include_router(router=lookup_router)


@app.get("/_health_check", response_class=Response)
def health_check() -> Response:
    """
    Health check endpoint to verify if the service is running normally.

    Returns:
        Response: A plain text response with content "ok" indicating service is healthy.
    """
    """
    监听服务是否正常运行
    :return:
    """
    return Response(content="ok", media_type="text/plain")
