import os
import re

from bs4 import BeautifulSoup

from op_purchase_ai.server.goods_info import image_extract_const
from op_purchase_ai.utils import request_utils


class CrawlService:

    def __init__(self):
        pass

    def extract_image_url_from_html(self, goods_url: str, html_filepath: str) -> list[str]:
        """
        从html文件中 提取图片链接
        :param goods_url: 商品链接
        :param html_filepath:
        :return:
        """
        if html_filepath is None:
            return []

        if not os.path.exists(html_filepath):
            raise FileNotFoundError(f'文件不存在: {html_filepath}')

        domain: str = request_utils.get_domain(goods_url)
        target_class_list: list[str] = image_extract_const.DOMAIN_TARGET_CLASS.get(domain)  # 只统计某些class的图片

        # 统计每个 class 对应的图片
        class_count: dict[str, set[str]] = {'__empty_class': set()}

        # 读取html文本
        with (open(html_filepath, 'r', encoding='utf-8') as file):
            html_content = file.read()
            soup = BeautifulSoup(html_content, 'html.parser')

            # 处理 img 标签
            img_tags = soup.find_all('img')
            for img in img_tags:
                if 'src' not in img.attrs:
                    continue
                image_url = img['src']
                self.handle_image_url(img, image_url, class_count)

            # 处理 div 标签中的 背景图片
            div_tags = soup.find_all('div')

            for div in div_tags:
                if 'style' not in div.attrs:
                    continue
                style_content = div['style']

                # 提取 background-image: url(...) 中的链接
                match = re.search(r'background-image\s*:\s*url\((.*?)\)', style_content, re.IGNORECASE)
                if match:
                    image_url = match.group(1).strip().strip('"').strip("'")  # 清理引号等符号
                    self.handle_image_url(div, image_url, class_count)

        if target_class_list is not None:
            # 统计出现次数最多的class
            max_class: str = ''
            max_class_count: int = 0
            for cls, url_list in class_count.items():
                if cls not in target_class_list:
                    continue
                if len(url_list) > max_class_count:
                    max_class = cls
                    max_class_count = len(url_list)

            if max_class == '':
                for cls, url_list in class_count.items():
                    if len(url_list) > max_class_count:
                        max_class = cls
                        max_class_count = len(url_list)

            return list(class_count[max_class])
        else:
            img_url_set = set()
            for cls, url_list in class_count.items():
                img_url_set.update(url_list)
            return list(img_url_set)

    def handle_image_url(self, html_block, image_url: str, class_count: dict[str, set[str]]):
        """
        处理从网页块中提取到的图片链接
        :param html_block:
        :param image_url:
        :param class_count:
        :return:
        """
        if not image_url.startswith('https://') and not image_url.startswith('http://'):
            return

        # 获取图片的域名
        image_domain = request_utils.get_domain(image_url)
        # 过滤广告域名
        if image_domain in image_extract_const.AD_DOMAIN:
            return

        if 'class' not in html_block.attrs:
            class_count['__empty_class'].add(image_url)
            return
        for cls in html_block['class']:
            if cls not in class_count:
                class_count[cls] = set()
            class_count[cls].add(image_url)


def __debug_extract_image_url_from_html():
    service = CrawlService()
    brand = 'reno'
    from op_purchase_ai.utils import os_utils
    json_filepath = os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'test_files', brand]),
        f'{brand}.json'
    )
    html_filepath = os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'test_files', brand]),
        f'{brand}.txt'
    )
    # import json
    # with open(json_filepath, 'r', encoding='utf-8') as file:
    #     data = json.load(file)
    #     with open(html_filepath, 'w', encoding='utf-8') as file2:
    #         file2.write(data['result']['html'])
    print(service.extract_image_url_from_html('https://www.adidas.com.cn/pdp?articleId=H51493', html_filepath))


if __name__ == '__main__':
    __debug_extract_image_url_from_html()
