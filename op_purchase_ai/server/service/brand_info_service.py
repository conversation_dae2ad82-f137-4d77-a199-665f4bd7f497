# -*- coding: utf-8 -*-
import io
import os
from typing import Optional, Any

import cv2
import numpy as np
import openpyxl
from PIL import Image
from cv2.typing import MatLike
from openpyxl.drawing.spreadsheet_drawing import TwoCellAnchor, OneCellAnchor
from openpyxl.utils import column_index_from_string
from openpyxl.worksheet.worksheet import Worksheet

from op_purchase_ai.utils import os_utils, cv2_utils
from op_purchase_ai.utils.log_utils import log


class _ImageInfo:
    """一个辅助类，用于存储图片对象及其在工作表中的行跨度信息。"""
    def __init__(self, image_obj, start_row: int, end_row: int):
        self.image_obj = image_obj
        self.start_row = start_row
        self.end_row = end_row
        self._pil_image: Optional[Image.Image] = None

    def get_pil_image(self) -> Image.Image:
        """
        懒加载方法，仅在需要时将 openpyxl 的图片数据转换为 PIL.Image 对象。
        这可以避免不必要的内存开销。
        """
        if self._pil_image is None:
            image_bytes = self.image_obj._data()
            self._pil_image = Image.open(io.BytesIO(image_bytes))
        return self._pil_image

    def get_cv_image(self) -> MatLike:
        pil_image = self.get_pil_image()
        if pil_image.mode == 'RGBA':
            pil_image = pil_image.convert('RGB')
        rgb = np.array(pil_image)

        # 图像的宽度和高度均应大于10像素，宽高比不应超过200:1或1:200。
        mini = 30.0
        if rgb.shape[0] < mini or rgb.shape[1] < mini:  # 设置为最少30 比较稳
            f = max(mini / rgb.shape[0], mini / rgb.shape[1])
            rgb = cv2.resize(rgb, (0, 0), fx=f, fy=f)
        
        # 转换为 RGB 格式的 OpenCV 图像
        rgb = cv2.cvtColor(rgb, cv2.COLOR_RGB2BGR)
        return rgb

    def __repr__(self) -> str:
        return f"_ImageInfo(start_row={self.start_row}, end_row={self.end_row})"


def map_rows_to_images_in_column(
    sheet: Worksheet,
    image_column: str
) -> dict[int, MatLike]:
    """
    从Excel工作表中提取图片，并将它们映射到对应的行号。
    图片可能跨列，但每行只有一张图片。

    Args:
        sheet: Excel工作表对象
        image_column (str): 图片所在的列名 (例如, 'E')。

    Returns:
        dict[int, MatLike]: 一个将行号映射到 opencv 图片对象的字典。
    """
    target_col_idx = column_index_from_string(image_column)
    row_to_image_map: dict[int, MatLike] = {}

    try:
        # 方法1：直接访问
        images1 = sheet._images

        # 方法2：通过images属性
        images2 = sheet.images if hasattr(sheet, 'images') else []

        # 方法3：检查所有可能的图片容器
        images3 = []
        for attr_name in dir(sheet):
            if 'image' in attr_name.lower():
                attr = getattr(sheet, attr_name)
                if isinstance(attr, list):
                    images3.extend(attr)

        print(f"方法1找到 {len(images1)} 张图片")
        print(f"方法2找到 {len(images2)} 张图片")
        print(f"方法3找到 {len(images3)} 张图片")

    except Exception as e:
        print(f"检查图片时出错: {e}")


    # 遍历工作表中的所有图片
    for image in sheet._images:
        image_row = 0
        image_start_col = 0
        image_end_col = 0

        if isinstance(image.anchor, OneCellAnchor):
            # OneCellAnchor: 图片在一个单元格内
            image_row = image.anchor._from.row + 1  # 转换为1-based
            image_start_col = image.anchor._from.col + 1
            image_end_col = image_start_col
        elif isinstance(image.anchor, TwoCellAnchor):
            # TwoCellAnchor: 图片可能跨多列
            from_row = image.anchor._from.row + 1
            from_col = image.anchor._from.col + 1
            to_row = image.anchor.to.row + 1
            to_col = image.anchor.to.col + 1
            
            # 确保图片不跨行（一行只有一张图片）
            if from_row == to_row:
                image_row = from_row
                image_start_col = min(from_col, to_col)
                image_end_col = max(from_col, to_col)
            else:
                continue  # 跳过跨行的图片
        else:
            continue  # 忽略其他类型的锚点

        # 检查图片是否包含目标列（图片可能跨列）
        if image_start_col <= target_col_idx <= image_end_col:
            # 如果该行已经有图片了，跳过（确保每行只有一张图片）
            if image_row in row_to_image_map:
                continue
            
            # 创建图片对象并转换为OpenCV格式
            image_info = _ImageInfo(image, image_row, image_row)
            row_to_image_map[image_row] = image_info.get_cv_image()

    return row_to_image_map


def read_excel_with_images_and_names(
    excel_file_path: str,
    name_column: str,
    image_column: str,
    start_row: int = 1,
) -> list[dict[str, Any]]:
    """
    从Excel文件中读取指定列的商品名称和图片。

    Args:
        excel_file_path (str): Excel文件路径
        name_column (str): 商品名称所在的列名 (例如, 'G')
        image_column (str): 图片所在的列名 (例如, 'A')
        start_row (int): 数据开始行号 (从1开始)

    Returns:
        list[dict[str, Any]]: 包含商品名称和图片的字典列表，格式为 [{'name': str, 'image': MatLike}, ...]
    """
    workbook = openpyxl.load_workbook(excel_file_path)
    sheet = workbook.active
    
    # 获取图片映射
    image_map = map_rows_to_images_in_column(sheet, image_column)
    
    result = []
    
    # 遍历指定列的单元格，获取商品名称
    name_col_idx = column_index_from_string(name_column)
    
    for row in range(start_row, sheet.max_row + 1):
        cell_value = sheet.cell(row=row, column=name_col_idx).value
        
        # 跳过空单元格
        if cell_value is None:
            continue
            
        # 获取该行对应的图片
        image = image_map.get(row, None)
        
        result.append({
            'name': str(cell_value),
            'image': image
        })
    
    workbook.close()
    return result


if __name__ == '__main__':
    base_dir = os_utils.get_path_under_work_dir(['.temp', 'brand_info', 'category3'])
    file_path = os.path.join(base_dir, 'test.xlsx')

    # 示例1: 只使用普通列图片
    result = read_excel_with_images_and_names(
        excel_file_path=file_path,
        name_column='A',
        image_column='B',
        start_row=3,
    )

    import zipfile
    import xml.etree.ElementTree as ET
    import xlwings as xw
    import os

    def detect_vml_images(excel_path):
        """检测VML格式的图片"""
        try:
            with zipfile.ZipFile(excel_path, 'r') as z:
                vml_images = []
                
                # 检查VML绘图文件
                for file in z.namelist():
                    if file.startswith('xl/drawings/vmlDrawing') and file.endswith('.vml'):
                        print(f"找到VML文件: {file}")
                        try:
                            content = z.read(file).decode('utf-8')
                            # 检查是否包含图片相关的标签
                            if '<image' in content or '<v:shape' in content:
                                vml_images.append({
                                    'file': file,
                                    'type': 'VML图片',
                                    'storage': 'xl/drawings/vmlDrawing',
                                    'content_preview': content[:200] + '...'
                                })
                        except Exception as e:
                            print(f"读取VML文件失败 {file}: {e}")
                
                return vml_images
        except Exception as e:
            print(f"检测VML图片失败: {e}")
            return []

    def detect_background_images(excel_path):
        """检测工作表背景图片"""
        try:
            with zipfile.ZipFile(excel_path, 'r') as z:
                # 检查工作表XML文件中的背景设置
                for file in z.namelist():
                    if file.startswith('xl/worksheets/sheet') and file.endswith('.xml'):
                        try:
                            content = z.read(file).decode('utf-8')
                            if 'background' in content.lower():
                                print(f"工作表可能包含背景图片: {file}")
                                return [{
                                    'file': file,
                                    'type': '工作表背景图片',
                                    'storage': '工作表背景',
                                    'content_preview': 'Found background reference'
                                }]
                        except Exception as e:
                            print(f"读取工作表XML失败 {file}: {e}")
                return []
        except Exception as e:
            print(f"检测背景图片失败: {e}")
            return []

    def get_excel_images_info(excel_path):
        """获取Excel中所有图片的详细信息"""
        app = xw.App(visible=False)
        wb = app.books.open(excel_path)

        images_info = []

        for sheet in wb.sheets:
            # 尝试多种方式获取图片
            print(f"\n检查工作表: {sheet.name}")
            
            # 方法1：标准pictures集合
            print(f"标准pictures集合找到: {len(sheet.pictures)} 张图片")
            for picture in sheet.pictures:
                # 尝试确定图片类型和存储位置
                image_type = "未知"
                storage_location = "未知"
                
                try:
                    # 获取图片对象的更多属性
                    if hasattr(picture, 'shape_type'):
                        shape_type = picture.shape_type
                        if shape_type == 13:  # msoPicture
                            image_type = "嵌入图片"
                            storage_location = "xl/media/"
                        elif shape_type == 1:  # msoAutoShape
                            image_type = "自选图形"
                            storage_location = "xl/drawings/"
                        elif shape_type == 7:  # msoOLEControlObject
                            image_type = "OLE控件对象"
                            storage_location = "xl/oleObjects/"
                        elif shape_type == 19:  # msoLinkedPicture
                            image_type = "链接图片"
                            storage_location = "外部链接"
                        elif shape_type == 3:  # msoLine
                            image_type = "线条图形"
                            storage_location = "xl/drawings/"
                    else:
                        # 通过名称推断
                        if hasattr(picture, 'name'):
                            name = picture.name.lower()
                            if any(keyword in name for keyword in ['ole', 'object', 'embed']):
                                image_type = "OLE对象"
                                storage_location = "xl/oleObjects/"
                            elif any(keyword in name for keyword in ['picture', 'image', 'img', 'pic']):
                                image_type = "普通图片"
                                storage_location = "xl/media/"
                            elif any(keyword in name for keyword in ['drawing', 'shape', 'graph']):
                                image_type = "绘图对象"
                                storage_location = "xl/drawings/"
                            elif any(keyword in name for keyword in ['chart', 'diagram']):
                                image_type = "图表对象"
                                storage_location = "xl/charts/"
                            elif any(keyword in name for keyword in ['control', 'button', 'form']):
                                image_type = "控件对象"
                                storage_location = "xl/activeX/"
                            elif any(keyword in name for keyword in ['comment', 'note']):
                                image_type = "批注图片"
                                storage_location = "xl/comments/"
                    
                    # 尝试获取更多属性
                    additional_info = {}
                    if hasattr(picture, 'locked'):
                        additional_info['locked'] = picture.locked
                    if hasattr(picture, 'locked_property'):
                        additional_info['locked_property'] = picture.locked_property
                    if hasattr(picture, 'print_object'):
                        additional_info['print_object'] = picture.print_object
                    if hasattr(picture, 'placement'):
                        placement_map = {1: "自由浮动", 2: "移动并调整大小", 3: "随单元格移动"}
                        additional_info['placement'] = placement_map.get(picture.placement, f"类型{picture.placement}")
                    
                    info = {
                        'name': picture.name,
                        'sheet': sheet.name,
                        'left': picture.left,
                        'top': picture.top,
                        'width': picture.width,
                        'height': picture.height,
                        'image_type': image_type,
                        'storage_location': storage_location,
                        'additional_info': additional_info
                    }
                    images_info.append(info)
                    
                    print(f"图片名称: {picture.name}")
                    print(f"所在工作表: {sheet.name}")
                    print(f"位置: Left={picture.left}, Top={picture.top}")
                    print(f"尺寸: {picture.width}x{picture.height}")
                    print(f"图片类型: {image_type}")
                    print(f"存储位置: {storage_location}")
                    if additional_info:
                        print(f"附加信息: {additional_info}")
                    print("-" * 50)
                    
                except Exception as e:
                    print(f"处理图片 {getattr(picture, 'name', 'Unknown')} 时出错: {e}")
                    continue
            
            # 方法2：尝试通过COM接口获取更多信息
            try:
                print(f"尝试COM接口检测...")
                # 这里可以添加win32com的调用
                # 但需要安装pywin32库
            except Exception as e:
                print(f"COM接口检测失败: {e}")
            
            # 方法3：尝试通过单元格内容检测图片引用
            try:
                print(f"检查单元格中的图片引用...")
                for row in range(1, 100):  # 检查前100行
                    for col in range(1, 26):  # 检查A-Z列
                        cell = sheet.range(row, col)
                        if cell.value and isinstance(cell.value, str):
                            if any(keyword in str(cell.value).lower() for keyword in ['image', 'picture', 'img', 'photo']):
                                print(f"在单元格 {chr(64+col)}{row} 找到可能的图片引用: {cell.value}")
            except Exception as e:
                print(f"单元格检测失败: {e}")

        wb.close()
        app.quit()
        return images_info

    # 使用示例
    print("=== 使用 xlwings 检测图片 ===")
    images_info = get_excel_images_info(file_path)
    
    print("\n=== 检测 VML 图片 ===")
    vml_images = detect_vml_images(file_path)
    for vml_img in vml_images:
        print(f"VML图片: {vml_img}")
    
    print("\n=== 检测背景图片 ===")
    bg_images = detect_background_images(file_path)
    for bg_img in bg_images:
        print(f"背景图片: {bg_img}")
    
    print(f"\n总统计:")
    print(f"xlwings检测到: {len(images_info)} 张图片")
    print(f"VML检测到: {len(vml_images)} 张图片")
    print(f"背景检测到: {len(bg_images)} 张图片")
    print(f"总计可能图片数: {len(images_info) + len(vml_images) + len(bg_images)}")

    # 显示合并模式的结果
    for i in result:
        if i['image'] is not None:
            cv2_utils.show_image(i['image'], win_name=i['name'], wait=0)
    print(len(result))
