import os
import threading
import time

from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.ocr.ocr_match_result import OcrMatchResult
from op_purchase_ai.ocr.onnx_ocr_matcher import OnnxOcrMatcher
from op_purchase_ai.utils import cv2_utils, os_utils
from op_purchase_ai.utils.log_utils import log


class OcrService:

    """
    负责调度多个模型
    """

    def __init__(self,
                 parallel: int = 1,
                 wait_timeout_seconds: float = 5,
                 ):
        self.wait_timeout_seconds: float = wait_timeout_seconds  # 等待超时时间
        self.lock_list: list[threading.Lock] = [
            threading.Lock()
            for _ in range(parallel)
        ]
        self.model_list: list[OnnxOcrMatcher] = [
            OnnxOcrMatcher()
            for _ in range(parallel)
        ]

    def init_model(self) -> None:
        """
        异步初始化ocr模型
        :return:
        """
        for idx in range(len(self.lock_list)):
            with self.lock_list[idx]:
                model = self.model_list[idx]
                model.init_model()

        log.info(f'OCR模型初始化完成')

    def ocr(self, img: MatLike, cls: bool = False) -> list[OcrMatchResult]:
        """
        找到一个上次使用时间最久远的模型 进行使用识别
        :param img: 图片 opencv 读取后的RGB格式
        :param cls: 是否使用方向识别
        :return: 识别结果
        """
        start_time = time.time()

        while True:
            if time.time() - start_time > self.wait_timeout_seconds:
                raise Exception('服务繁忙 未有可用模型')

            for i in range(len(self.lock_list)):
                lock = self.lock_list[i]
                if not lock.acquire(blocking=False):
                    continue

                try:
                    log.info(f'[OCR] 占用模型 {i} 开始识别')
                    model = self.model_list[i]
                    result_list: list[OcrMatchResult] = model.ocr(img, cls)
                    # paddleocr 有bug 多次使用可能出错 因此每次使用完重新初始化
                    # https://github.com/PaddlePaddle/PaddleOCR/discussions/12623
                    model.init_model()
                    return result_list
                finally:
                    log.info(f'[OCR] 释放模型 {i}')
                    lock.release()

            time.sleep(0.1)  # 自旋等待


def __debug():
    ocr_service = OcrService(parallel=2)
    ocr_service.init_model()
    from concurrent.futures import ThreadPoolExecutor
    async_init_executor = ThreadPoolExecutor(thread_name_prefix='async_init_executor', max_workers=4)
    img1 = cv2_utils.read_image(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'pdf2images', 'hcg']),
            'hcg_3.png'
        )
    )
    img2 = cv2_utils.read_image(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'pdf2images', 'hcg']),
            'hcg_2.png'
        )
    )
    f1 = async_init_executor.submit(ocr_service.ocr, img1, False)
    f2 = async_init_executor.submit(ocr_service.ocr, img2, False)

    cv2_utils.show_image(img1, rects=f1.result(), max_height=1000, wait=0)
    cv2_utils.show_image(img2, rects=f2.result(), max_height=1000, wait=0)


if __name__ == '__main__':
    __debug()
