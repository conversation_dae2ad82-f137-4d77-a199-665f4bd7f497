import threading
import time

from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.utils.log_utils import log
from op_purchase_ai.yolo.yolo_match_result import YoloMatchResult
from op_purchase_ai.yolo.yolov8_onnx_det import Yolov8Detector


class YoloService:

    """
    负责调度多个模型
    """

    def __init__(self,
                 model_name: str = 'yolov8x-det',
                 parallel: int = 1,
                 wait_timeout_seconds: float = 5
                 ):
        self.wait_timeout_seconds: float = wait_timeout_seconds  # 等待超时时间
        self.lock_list: list[threading.Lock] = [
            threading.Lock()
            for _ in range(parallel)
        ]
        self.model_list: list[Yolov8Detector] = [
            Yolov8Detector(model_name=model_name)
            for _ in range(parallel)
        ]

    def init_model(self) -> None:
        """
        异步初始化ocr模型
        :return:
        """
        for idx in range(len(self.lock_list)):
            with self.lock_list[idx]:
                model = self.model_list[idx]
                model.init_model()
        log.info(f'YOLO模型初始化完成 {self.model_list[0].model_name}')

    def run(self, img: MatLike) -> list[YoloMatchResult]:
        """
        找到一个上次使用时间最久远的模型 进行使用识别
        :param img: 图片 opencv 读取后的RGB格式
        :return: 识别结果
        """
        start_time = time.time()

        while True:
            if time.time() - start_time > self.wait_timeout_seconds:
                raise Exception('服务繁忙 未有可用模型')

            for i in range(len(self.lock_list)):
                lock = self.lock_list[i]
                if not lock.acquire(blocking=False):
                    continue

                try:
                    log.info(f'[YOLO] 占用模型 {i} 开始识别')
                    model = self.model_list[i]
                    return model.run(img)
                finally:
                    log.info(f'[YOLO] 释放模型 {i}')
                    lock.release()

            time.sleep(0.1)  # 自旋等待
