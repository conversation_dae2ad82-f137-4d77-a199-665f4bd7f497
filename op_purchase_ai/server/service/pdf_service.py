import os
import re
from typing import Optional

import fitz
import numpy as np
import pymupdf
from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.ocr.ocr_match_result import OcrMatchResult
from op_purchase_ai.server.context import AppContext
from op_purchase_ai.utils import os_utils, cv2_utils, detect_result_utils
from op_purchase_ai.utils.log_utils import log
from op_purchase_ai.yolo.yolo_match_result import Yolo<PERSON>atchResult, DetectClass


class PdfService:

    def __init__(self, ctx: AppContext):
        self.ctx: AppContext = ctx

    def pdf_to_images(self, pdf_filepath: str, page_start: Optional[int] = None, page_end: Optional[int] = None) -> list[tuple[int, str]]:
        """
        将PDF的导出成每页的图片 保存到临时文件夹中
        :param pdf_filepath: PDF的文件路径
        :param page_start: 解析页面的最小值(包含) 从1开始
        :param page_end: 解析页面的最大值(包含) 从1开始
        :return: 页码 和 对应页图片的临时路径
        """
        doc: pymupdf.Document = pymupdf.open(pdf_filepath)
        filename = os.path.basename(pdf_filepath)  # 由本应用产生的临时文件名称 都是 时间戳_原文件名.pdf
        filename = filename[filename.find('_') + 1:-4]  # 提取原文件名部分

        temp_dir = os_utils.get_path_under_work_dir(['.temp', 'pdf2images', filename])
        temp_filepath_list: list[tuple[int, str]] = []

        try:
            for page_index in range(1, len(doc) + 1):
                if page_start is not None and page_index < page_start:
                    continue
                if page_end is not None and page_index > page_end:
                    continue

                page: pymupdf.Page = doc[page_index - 1]
                page_image, _ = self.page_to_image(page)

                temp_filepath = os.path.join(temp_dir, f'{filename}_{page_index}.png')
                cv2_utils.save_image(page_image, temp_filepath)
                temp_filepath_list.append((page_index, temp_filepath))

            return temp_filepath_list
        except Exception as e:
            # 出现异常时 删除所有临时文件
            log.info('PDF图片切割异常 删除临时文件')
            for _, temp_filepath in temp_filepath_list:
                if os.path.exists(temp_filepath):
                    os.remove(temp_filepath)
            raise e

    def page_to_image(
            self,
            page: pymupdf.Page,
            scale_factor: float | None = None,
    ) -> MatLike:
        """
        将PDF的某页 转换成一张图片

        Args:
            page: PDF的页
            scale_factor: 缩放比例

        Returns:
            MatLike: 图片
        """
        # 获取缩放比例
        if scale_factor is None:
            scale_factor = self.get_page_scale_factor(page)

        # 设置缩放矩阵 不改变原来比例
        mat = fitz.Matrix(scale_factor, scale_factor)

        # 将页面渲染为图像（Pixmap）
        pix = page.get_pixmap(matrix=mat)

        # 将Pixmap转换为NumPy数组 RGB(A)
        page_image: MatLike = np.frombuffer(pix.samples, dtype=np.uint8).reshape(pix.height, pix.width, pix.n)

        return page_image, scale_factor

    def pdf_to_one_image(self, pdf_filepath: str, page_start: Optional[int] = None, page_end: Optional[int] = None) -> str:
        """
        将PDF的指定页码 合并成一张图片 导出保存到临时文件夹中
        Args:
            pdf_filepath:
            page_start:
            page_end:

        Returns:

        """
        pass

    @staticmethod
    def get_page_scale_factor(page: pymupdf.Page) -> float:
        """
        计算该页转换成图片所需的缩放比例

        - 短边至少是 1000像素
        - 提取的文本 单字符最小 2*2 像素
        Args:
            page:

        Returns:

        """
        text_instances = page.get_text("dict")["blocks"]
        page_width: float = page.rect.width
        page_height: float = page.rect.height

        min_scale: float = 1.0

        # 如果页面较小 则缩放短边到至少1000
        min_page_size: float = 1000.0
        if page_width <= page_height and page_width < min_page_size:
            min_scale = min_page_size / page_width
        elif page_height <= page_width and page_height < min_page_size:
            min_scale = min_page_size / page_height

        # 如果字体较小 则缩放至单个字符 2*2 像素
        min_char_size: float = 2.0
        for block in text_instances:
            if block["type"] != 0:  # 0 表示文本块
                continue
            for line in block["lines"]:
                for span in line["spans"]:
                    text = span["text"]
                    bbox = span["bbox"]  # 文本的边界框 [x0, y0, x1, y1]
                    w = int(bbox[2] - bbox[0])
                    h = int(bbox[3] - bbox[1])
                    if len(text) == 0:
                        continue

                    # 宽度 按字符数量计算
                    if w > 0 and len(text) * min_char_size > w and len(text) * min_char_size / w > min_scale:
                        min_scale = len(text) * min_char_size / w

                    # 高度 按当前行高度计算
                    if h > 0 and min_char_size > h and min_char_size / h > min_scale:
                        min_scale = min_char_size / h

        return min_scale

    def extract_word_and_image(
            self,
            pdf_file_path: str,
            page_num: int,
            page_image: MatLike = None,
    ) -> tuple[list[OcrMatchResult], list[YoloMatchResult]]:
        """
        从PDF指定分页中提取文本和图片
        :param pdf_file_path: pdf临时文件路径
        :param page_num: 页码 从1开始
        :param page_image: pdf分页图片 可能经过缩放 传入时需要将pdf分页提取内容的坐标按对应比例缩放
        :return: 提取结果
        """
        # 获取PDF分页内容
        doc: pymupdf.Document = pymupdf.open(pdf_file_path)
        page: pymupdf.Page = doc[page_num - 1]

        # 获取缩放比例
        if page_image is None:
            scale_factor: float = 1
        else:
            image_height: int = page_image.shape[0]
            image_width: int = page_image.shape[1]
            page_height: int = page.rect.height
            page_width: int = page.rect.width

            scale_factor: float = image_height / page_height

        return self.extract_word_ang_image_from_page(page, scale_factor)

    def extract_word_ang_image_from_page(
            self,
            page: pymupdf.Page,
            scale_factor: float,
    ) -> tuple[list[OcrMatchResult], list[YoloMatchResult]]:
        """
        从PDF分页中提取文本和图片，只提取，不做识别

        Args:
            page: PDF分页
            scale_factor: 缩放比例 用于坐标对齐

        Returns:
            tuple[list[OcrMatchResult], list[YoloMatchResult]]: 文本结果和图片结果
        """
        # 获取PDF分页上的文本
        text_list: list[OcrMatchResult] = []
        text_instances = page.get_text("dict")["blocks"]

        for block in text_instances:
            if block["type"] == 0:  # 0 表示文本块
                for line in block["lines"]:
                    for span in line["spans"]:
                        text = span["text"]
                        bbox = span["bbox"]  # 文本的边界框 [x0, y0, x1, y1]
                        text_result = OcrMatchResult(
                            confidence=1,
                            x=int(bbox[0] * scale_factor),
                            y=int(bbox[1] * scale_factor),
                            w=int((bbox[2] - bbox[0]) * scale_factor),
                            h=int((bbox[3] - bbox[1]) * scale_factor),
                            template_scale=scale_factor,
                            data=text
                        )
                        text_list.append(text_result)

        # 将相邻的文本合并
        merged_text_list: list[OcrMatchResult] = []
        ocr_line_list: list[list[OcrMatchResult]] = detect_result_utils.merge_ocr_to_lines(text_list)
        for ocr_line in ocr_line_list:
            last_ocr_item: Optional[OcrMatchResult] = None
            for ocr_item in ocr_line:
                if last_ocr_item is None:
                    last_ocr_item = ocr_item
                    continue

                # 按平均每个字符的宽度来判断 如果两个文本间距离不到一个字符 则认为是连续的文本
                last_avg_char_width = last_ocr_item.w * 1.0 / len(last_ocr_item.data)
                avg_char_width = ocr_item.w * 1.0 / len(ocr_item.data)
                if last_ocr_item.x2 + min(last_avg_char_width, avg_char_width) >= ocr_item.x1:
                    last_ocr_item.merge(ocr_item)
                else:
                    merged_text_list.append(last_ocr_item)
                    last_ocr_item = ocr_item

            if last_ocr_item is not None:
                merged_text_list.append(last_ocr_item)

        # 部分文档可能连续字符之间有多个空格 这里将空格合并
        for ocr_item in merged_text_list:
            ocr_text = ocr_item.data
            ocr_item.data = re.sub(r'\s+', ' ', ocr_text.strip())

        # 获取PDF分页上的图片
        image_list: list[YoloMatchResult] = []

        image_info_list = page.get_image_info()
        for image_info in image_info_list:
            bbox = image_info['bbox']

            match_result = YoloMatchResult(
                confidence=1,
                x=int(bbox[0] * scale_factor),
                y=int(bbox[1] * scale_factor),
                w=int((bbox[2] - bbox[0]) * scale_factor),
                h=int((bbox[3] - bbox[1]) * scale_factor),
                template_scale=scale_factor,
                data=DetectClass(class_id=-1, class_name='unknown', class_category='unknown')
            )

            if match_result.w == page.rect.width or match_result.h == page.rect.height:
                # 过大的图片就忽略
                continue

            image_list.append(match_result)

        return merged_text_list, image_list

    def get_word_and_image_by_page(
            self,
            pdf_file_path: str,
            image_scale: float | None = None,
    ) -> dict[int, tuple[list[OcrMatchResult], list[YoloMatchResult]]]:
        """
        对PDF文件的内容进行提取和识别 最终按页返回

        Args:
            pdf_file_path: PDF文件路径
            image_scale: 使用OCR识别时 图片的缩放比例

        Returns:
            dict[int, tuple[list[OcrMatchResult], list[YoloMatchResult]]]: 页码和识别结果
        """
        all_pages_content = {}
        doc: pymupdf.Document | None = None

        try:
            # 打开PDF文档
            doc = pymupdf.open(pdf_file_path)
            total_pages = len(doc)
            log.debug(f'[PDF索引] PDF文件共 {total_pages} 页')

            for page_num in range(1, total_pages + 1):
                page: pymupdf.Page = doc[page_num - 1]
                page_image, scale_factor = self.page_to_image(page, image_scale)

                # 使用OCR识别文本
                ocr_result_list = self.ctx.ocr_service.ocr(page_image)

                # 使用PDF服务提取文本
                text_result_list, image_result_list = self.extract_word_ang_image_from_page(
                    page=page,
                    scale_factor=scale_factor,
                )

                # 过滤重复内容
                filter_ocr_result_list, _ = detect_result_utils.filter_detect_result(
                    ocr_result_list, [], text_result_list, image_result_list
                )

                all_pages_content[page_num] = (filter_ocr_result_list + text_result_list, image_result_list)

            return all_pages_content
        except Exception as e:
            log.error(f'[PDF索引] PDF内容提取失败: {str(e)}')
            return {}
        finally:
            if doc is not None:
                doc.close()


def __debug_pdf_to_images(brand: str, pdf_type: str = 'excel'):
    ctx = AppContext()
    pdf_service = PdfService(ctx)
    pdf_service.pdf_to_images(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'test_files', pdf_type]),
            f'{brand}.pdf'
        )
    )


def __debug_extract_word_and_image():
    pdf_service = PdfService()
    brand_name = 'hh'
    pdf_path = os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'test_files', brand_name]),
        f'{brand_name}.pdf'
    )
    target_page_idx = 1
    page_image_list = pdf_service.pdf_to_images(pdf_path, 1, 1)
    page: MatLike = cv2_utils.read_image(page_image_list[target_page_idx - 1][1])
    text_list, image_list = pdf_service.extract_word_and_image(pdf_path, target_page_idx, page_image=1)
    from op_purchase_ai.basic.rectangle import Rect
    # cv2_utils.show_image(page, text_list + image_list, max_height=1000, wait=0)
    cv2_utils.show_image(page, [
        Rect(28, 73, 976, 375),
        Rect(498, 124, 654, 186),
        Rect(704, 64, 777, 251),
        Rect(494, 288, 556, 314),
        Rect(558, 288, 617, 314),
    ], max_height=1000, wait=0)


if __name__ == '__main__':
    __debug_pdf_to_images('adidas2')
    # __debug_extract_word_and_image()
