import time

from op_purchase_ai.dify.dify_workflow_client import DifyWorkflowClient
from op_purchase_ai.dify.models.workflow_run import WorkflowRunRequest, WorkflowRunCompletionResponse
from op_purchase_ai.utils.log_utils import log


class OpPurchaseDifyService:

    def __init__(self, base_url: str):
        self.base_url: str = base_url
        self.user: str = 'op-purchase-serving.ai.vip.com'

    def run_workflow(
            self,
            run_name: str,
            api_key: str,
            request: WorkflowRunRequest,
            base_url: str | None = None,
            retry_times: int = 3,
    ) -> WorkflowRunCompletionResponse:
        """
        调用Dify的workflow接口，包含一些自动重试功能

        Args:
            run_name: 运行名称 用于日志显示
            api_key: workflow的ApiKey
            request: 请求入参
            base_url: Dify url
            retry_times: 重试次数

        Returns:
            WorkflowRunCompletionResponse: 响应结果
        """
        client = DifyWorkflowClient(
            base_url=self.base_url if base_url is None else base_url,
            api_key=api_key,
        )

        total_cnt = retry_times + 1
        for idx in range(total_cnt):
            log.info(f'[{run_name}] 入参: {request.model_dump_json()}')
            response: WorkflowRunCompletionResponse = client.run(
                request=request
            )
            log.info(f'[{run_name}] 结果: {response.model_dump_json()}')
            if response.data.status != 'succeeded':
                # 出现错误 重试一次
                log.warning(f'[{run_name}] 运行出错 当前 {idx + 1}/{total_cnt} 次')
                if response.data.error is not None and response.data.error.find('429') != -1:
                    log.warning(f'[{run_name}] 大模型限流 当前 {idx + 1}/{total_cnt} 次')
                    time.sleep(10)  # 触发了限流 等待一段时间后再继续
                continue
            else:
                return response

        raise Exception('请求Dify失败')