import json
import os
import re
import time

import requests

from op_purchase_ai.server.response.vfs_response import FileUploadResult
from op_purchase_ai.utils import os_utils, time_utils
from op_purchase_ai.utils.log_utils import log


class VfsFileWrapper:

    def __init__(self, fid: str, filepath: str, last_used_time: float):
        self.fid: str = fid  # VFS系统的文件ID
        self.filepath: str = filepath  # 下载后保存的文件路径
        self.last_used_time: float = last_used_time  # 最后一次使用的时间


class VfsService:

    def __init__(self, group: str):
        """
        https://wiki.corp.vipshop.com/pages/viewpage.action?pageId=167087802
        VFS文件服务
        :param group: 登记使用的组别
        """
        self._group: str = group  # 登记使用的组别
        self._cache_files: dict[str, VfsFileWrapper] = {}  # 缓存列表

    def upload(self, filepath: str) -> FileUploadResult:
        """
        上传文件
        :param filepath: 需要上传的文件路径
        :return: 上传后vfs里的文件id
        """
        upload_url = f'http://vis-fs.api.vip.com/vfs/rest/upload/new/{self._group}.json'

        with open(filepath, 'rb') as file:
            filename: str = os.path.basename(filepath)
            files = {'file': (filename, file)}
            response = requests.post(upload_url, files=files)
            if response.status_code == 200:
                response_data = response.json()
                vfs_code = response_data.get('code', '')
                vfs_msg = response_data.get('msg', '')

                if vfs_code != 200:
                    error_msg = f'上传文件到vfs失败 filename={filename} code={vfs_code} msg={vfs_msg}'
                    log.error(error_msg)
                    raise Exception(error_msg)

                fid = response_data.get('result', {}).get('fid', '')
                return FileUploadResult(fid=fid, file_name=filename)
            else:
                error_msg = f'上传文件到vfs失败 filename={filename} code={response.status_code}'
                log.error(error_msg)
                raise Exception(error_msg)

    def download(self, fid: str) -> str:
        """
        下载文件
        :param fid: vfs里的文件id
        :return: 下载后的文件路径
        """
        if fid in self._cache_files:  # 已经缓存了
            cache = self._cache_files[fid]
            cache.last_used_time = time.time()
            return cache.filepath

        download_url = f'http://vis-fs.api.vip.com/vfs/rest/download/{fid}'

        response = requests.get(download_url)
        vfs_code, vfs_msg = self.get_vfs_response_from_header(response)
        if response.status_code == 200 and vfs_code == '200':
            save_filepath = os.path.join(
                os_utils.get_path_under_work_dir(['.temp', 'vfs']),
                self.get_filename_from_header(response)
            )
            with open(save_filepath, 'wb') as file:
                file.write(response.content)

            log.info(f'下载文件 fid={fid} filepath={save_filepath}')
            return save_filepath
        else:
            if response.status_code != 200:
                error_msg = f'从vfs下载文件失败 fid={fid} code={response.status_code}'
            else:
                error_msg = f'从vfs下载文件失败 fid={fid} code={vfs_code} msg={vfs_msg}'
            log.error(error_msg)
            raise Exception(error_msg)

    def get_filename_from_header(self, response) -> str:
        """
        从header中获取下载的文件名
        并添加时间戳前缀
        :param response: 响应
        :return: 文件名
        """
        original_filename = ''
        content_disposition = response.headers.get('Content-Disposition')
        if content_disposition:
            match = re.search(r'filename="([^"]+)"', content_disposition)
            if match:
                original_filename = match.group(1)

        return f'{time_utils.get_full_timestamp_str()}_{original_filename}'

    def get_vfs_response_from_header(self, response) -> tuple[str, str]:
        """
        从header中获取vfs的响应编码和内容
        :param response:
        :return:
        """
        return (
            response.headers.get('vfs_code'),
            response.headers.get('vfs_msg')
        )


def __upload_for_analyse_file_excel(brand = 'hcg'):
    vfs_service = VfsService(group='op_purchase_ai')

    fid = vfs_service.upload(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'test_files', 'excel']),
            f'{brand}.pdf'
        )
    )
    print(fid)


def __upload_for_goods_info():
    vfs_service = VfsService(group='op_purchase_ai')
    brand = 'hcg'
    fid = vfs_service.upload(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'test_files', brand]),
            f'{brand}.xlsx'
        )
    )
    print(fid)

    fid = vfs_service.upload(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'test_files', brand]),
            f'{brand}.txt'
        )
    )
    print(fid)

    fid = vfs_service.upload(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'test_files', brand]),
            f'{brand}.jpg'
        )
    )
    print(fid)


def __upload_for_lookup_invoice(brand: str):
    vfs_service = VfsService(group='op_purchase_ai')

    fid = vfs_service.upload(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'test_files']),
            f'{brand}.pdf'
        )
    )
    print('ORIGINAL PDF: ', fid)


    fid = vfs_service.upload(
        os.path.join(
            os_utils.get_path_under_work_dir(['.temp', 'result_files']),
            f'{brand}.xlsx'
        )
    )
    print('RESULT EXCEL: ', fid)


def __debug_download():
    vfs_service = VfsService(group='op_purchase_ai')
    fid = '18c78850599a4caaa465f607a8a0287701c1ad86d65634d9aa1815a8ebc8b86dd4866294b0776ee4d8da020adcc8022a6bd30bb73f4d6e6ba42bc92612915e0bafbfc4532497aaac2bd465daa882edc9de84aa61493da06d311ab48da9f1b376f71f86c3d587944c4cf9196496f907264058e8cfec09836'
    vfs_service.download(fid)


if __name__ == '__main__':
    # __upload_for_analyse_file_excel(brand='adidas2')
    # __upload_for_lookup_invoice('klss26')
    __debug_download()