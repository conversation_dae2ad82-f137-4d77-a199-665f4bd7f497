from pydantic import BaseModel, Field

from op_purchase_ai.server.response.common_response import CommonResponse


class GIWebImageUrlExtractRequest(BaseModel):

    goods_url: str = Field(default='', description='商品url')
    fid: str = Field(default='', description='HTML文件的FID')


class GIWebImageUrlExtractResponse(CommonResponse):

    image_url_list: list[str] = Field(default=[], description='图片url列表')
