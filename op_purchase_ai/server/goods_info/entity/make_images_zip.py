from pydantic import BaseModel, Field

from op_purchase_ai.server.response.common_response import CommonResponse


class GIMakeImagesZipRequest(BaseModel):

    zip_name: str = Field(default='', description='压缩包名称')
    image_fid_list: list[str] = Field(default=[], description='图片的FID列表')
    image_size: str = Field(default='', description='图片尺寸')


class GIMakeImagesZipResponse(CommonResponse):

    fid: str = Field(default='', description='压缩包的FID')
