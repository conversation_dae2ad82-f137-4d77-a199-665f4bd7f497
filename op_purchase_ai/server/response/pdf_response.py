from typing import Optional

from pydantic import Field, BaseModel

from op_purchase_ai.server.response.common_response import CommonResponse
from op_purchase_ai.server.response.vfs_response import FileUploadResult


class Pdf2ImagesRequest(BaseModel):

    fid: str = Field(default='', description="vfs系统的文件ID")
    page_start: Optional[int] = Field(default=None, description="解析页面的最小值(包含) 从1开始")
    page_end: Optional[int] = Field(default=None, description="解析页面的最大值(包含) 从1开始")


class Pdf2ImagesResponse(CommonResponse):

    result: dict[int, FileUploadResult] = Field(default={}, description="分页上传的结果")
