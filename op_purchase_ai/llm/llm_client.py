from openai import OpenAI, Stream
from openai.types.chat import ChatCompletionChunk


class LlmClient:

    def __init__(self, base_url: str, api_key: str, log_stream: bool):
        """
        https://wiki.corp.vipshop.com/pages/viewpage.action?pageId=2672758515
        """
        self.client = OpenAI(
            api_key=api_key,
            base_url=base_url,
        )
        self.log_stream: bool = log_stream  # 打印流式响应 本地开发时候开启

    def chat_completion(self, messages: list[dict], model: str = "DeepSeekV3") -> str:
        """
        调用对话接口
        :param messages: 消息列表
        :param model: 模型名称
        :return: 响应结果
        """
        completion: Stream[ChatCompletionChunk] = self.client.chat.completions.create(
            model=model,
            messages=messages,
            stream=True,
        )

        response = ''
        for chunk in completion:
            chunk_json = chunk.model_dump()
            for choice in chunk_json['choices']:
                choice_delta = choice['delta']
                if 'content' in choice_delta and choice_delta['content'] is not None:
                    chunk_message = choice_delta['content']
                    if self.log_stream:
                        print(chunk_message, end='')
                    response += chunk_message
                elif 'reasoning_content' in choice_delta and choice_delta['reasoning_content'] is not None:
                    reasoning_content = choice_delta['reasoning_content']
                    if self.log_stream:
                        print(reasoning_content, end='')
        if self.log_stream:
            print('\n')

        return response

    def chat_one_round(self, message: str, model: str = "DeepSeekV3") -> str:
        """
        进行单轮对话 只需传入本次的问题即可
        :param message: 本次的问题
        :param model: 模型名称
        :return: 响应结果
        """
        messages = [
            {"role": "user", "content": message}
        ]
        return self.chat_completion(messages, model)


def __debug():
    prompt = '''
当前有一个json数组，代表从图像中提取的文本识别结果，每个元素包含以下信息:
1. "det_idx": 识别结果的唯一标识
2. "det_rect": 识别结果的区域矩形的左上角和右下角坐标
3. "rec_text": 识别的文本

帮我从文本识别结果中，提取一个商品信息json，包括以下信息:
0. key="色号" value=一个文本 例如 Black 001
1. key="色号唯一标识" value=一个整数数组 包含组成"色号"的识别结果唯一标识
2. key="币种" value=一个文本 例如 EUR
3. key="币种唯一标识" value=一个整数数组 包含组成"币种"的识别结果唯一标识
4. key="商品名称" value=一个文本 例如 Romy Hobo Bag
5. key="商品名称唯一标识" value=一个整数数组 包含组成"商品名称"的识别结果唯一标识
6. key="货号" value=一个文本 例如 170964-001
7. key="货号唯一标识" value=一个整数数组 包含组成"货号"的识别结果唯一标识
8. key="图片" value=一个文本 例如 
9. key="图片唯一标识" value=一个整数数组 包含组成"图片"的识别结果唯一标识
10. key="零售价" value=一个文本 例如 625.00
11. key="零售价唯一标识" value=一个整数数组 包含组成"零售价"的识别结果唯一标识
12. key="尺码" value=一个文本 例如 OS
13. key="尺码唯一标识" value=一个整数数组 包含组成"尺码"的识别结果唯一标识
14. key="批发价" value=一个文本 例如 236.00
15. key="批发价唯一标识" value=一个整数数组 包含组成"批发价"的识别结果唯一标识


商品信息的提取遵循以下规则:
1. 不需要考虑文本识别错误的情况
2. 商品信息可以由多个相邻的识别结果拼接起来
3. 商品信息可以只提取文本中的一部分

最终按json格式输出包含一个元素的数组，只输出json部分

以下是文本识别结果的json:

[{"det_idx":1,"det_rect":"[ [ 26, 27 ], [ 197, 40 ] ]","rec_text":"EU F25 HB&SLG AUG_TORY BURCH"},
{"det_idx":3,"det_rect":"[ [ 211, 63 ], [ 266, 77 ] ]","rec_text":"Romy Hobo Bag"},
{"det_idx":5,"det_rect":"[ [ 211, 82 ], [ 267, 93 ] ]","rec_text":"Wholesale: EUR"},
{"det_idx":6,"det_rect":"[ [ 212, 74 ], [ 253, 84 ] ]","rec_text":"170964-001"},
{"det_idx":9,"det_rect":"[ [ 210, 93 ], [ 239, 102 ] ]","rec_text":"236.00"},
{"det_idx":10,"det_rect":"[ [ 211, 100 ], [ 276, 111 ] ]","rec_text":"Retall: EUR 625.00"},
{"det_idx":13,"det_rect":"[ [ 216, 118 ], [ 264, 132 ] ]","rec_text":"Black 001"},
{"det_idx":15,"det_rect":"[ [ 210, 141 ], [ 246, 152 ] ]","rec_text":"Sizes: OS"}]
'''
    client = LlmClient(
        base_url='http://10.46.132.169:1025/v1',
        api_key='xxx',
        log_stream=True
    )
    client.chat_one_round(prompt, 'DeepSeekV3')


if __name__ == '__main__':
    __debug()

