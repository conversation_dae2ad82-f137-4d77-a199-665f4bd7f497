def remove_think_tag(text: str) -> str:
    """
    去除响应里的思考标签
    :param text: llm响应文本
    :return: 去除思考标签后的文本
    """
    end_tag = '</think>'
    end_idx = text.rfind(end_tag)
    if end_idx != -1:
        return text[end_idx + len(end_tag):]
    else:
        return text


def extract_json_str(text: str) -> str:
    """
    从LLM结果中 提取json字符串部分的内容
    """
    # 找到markdown的起点
    md_json_start = '```json'
    md_start = '```'
    md_end = '```'
    start_idx = text.rfind(md_json_start)

    if start_idx == -1:
        start_idx = text.rfind(md_start)
        if start_idx == -1:
            pass
        else:
            start_idx += len(md_start)
            end_idx = text.find(md_end, start_idx)
    else:
        start_idx += len(md_json_start)
        end_idx = text.find(md_end, start_idx)

    return text[start_idx:end_idx] if start_idx != -1 else text
    