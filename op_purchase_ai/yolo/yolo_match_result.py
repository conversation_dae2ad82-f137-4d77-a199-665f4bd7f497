import time
from typing import Optional, List

from cv2.typing import <PERSON><PERSON><PERSON>
from pydantic import Field, BaseModel

from op_purchase_ai.basic.match_result import MatchResult


class DetectContext:

    def __init__(self, raw_image: MatLike, run_time: Optional[float] = None):
        """
        推理过程的上下文
        用于保存临时变量
        """
        self.run_time: float = time.time() if run_time is None else run_time
        """识别时间"""

        self.img: MatLike = raw_image
        """预测用的图片"""

        self.img_height: int = raw_image.shape[0]
        """原图的高度"""

        self.img_width: int = raw_image.shape[1]
        """原图的宽度"""

        self.label_list: Optional[List[str]] = None
        """只检测特定的标签 见 labels.csv 的label"""

        self.category_list: Optional[List[str]] = None
        """只检测特定分类的标签 见 labels.csv 的category"""

        self.conf: float = 0.7
        """检测时用的置信度阈值"""

        self.iou: float = 0.5
        """检测时用的IOU阈值"""

        self.scale_height: int = 0
        """缩放后的高度"""

        self.scale_width: int = 0
        """缩放后的宽度"""


class DetectClass(BaseModel):

    class_id: int = Field(..., description="识别类别的ID")
    class_name: str = Field(..., description="识别类别的名称")
    class_category: Optional[str] = Field(None, description="识别类别的分类")


class YoloMatchResult(MatchResult):

    data: DetectClass = Field(..., description="目标检测的类别")
