import os
from typing import List, Optional

import onnxruntime as ort


class Yolov8OnnxBase:

    def __init__(self,
                 model_name: str,
                 model_parent_dir_path: str = os.path.abspath(__file__),  # 默认使用本文件的目录
                 ):
        self.model_name: str = model_name
        self.model_parent_dir_path: str = model_parent_dir_path
        self.model_dir_path = os.path.join(self.model_parent_dir_path, self.model_name)

        # 从模型中读取到的输入输出信息
        self.session: Optional[ort.InferenceSession] = None
        self.input_names: List[str] = []
        self.onnx_input_width: int = 0
        self.onnx_input_height: int = 0
        self.output_names: List[str] = []

    def init_model(self) -> None:
        """
        加载模型
        :return:
        """
        onnx_path = os.path.join(self.model_dir_path, 'model.onnx')
        if not os.path.exists(onnx_path):
            raise FileNotFoundError(f'模型文件不存在')

        # availables = ort.get_available_providers()
        providers = ['CPUExecutionProvider']
        self.session = ort.InferenceSession(
            onnx_path,
            providers=providers
        )
        self.get_input_details()
        self.get_output_details()

    def get_input_details(self):
        model_inputs = self.session.get_inputs()
        self.input_names = [model_inputs[i].name for i in range(len(model_inputs))]

        shape = model_inputs[0].shape
        self.onnx_input_height = shape[2]
        self.onnx_input_width = shape[3]

    def get_output_details(self):
        model_outputs = self.session.get_outputs()
        self.output_names = [model_outputs[i].name for i in range(len(model_outputs))]
