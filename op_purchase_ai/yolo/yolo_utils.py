from typing import List, <PERSON><PERSON>

import cv2
import numpy as np
from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.yolo.yolo_match_result import YoloMatchResult

_COLORS = np.random.default_rng(3).uniform(0, 255, size=(100, 3))


def scale_input_image_u(image: <PERSON><PERSON><PERSON>, onnx_input_width: int, onnx_input_height: int) -> Tuple[np.ndarray, int, int]:
    """
    按照 ultralytics 的方式，将图片缩放至模型使用的大小
    参考 https://github.com/orgs/ultralytics/discussions/6994?sort=new#discussioncomment-8382661
    :param image: 输入的图片 RBG通道
    :param onnx_input_width: 模型需要的图片宽度
    :param onnx_input_height: 模型需要的图片高度
    :return: 缩放后的图片 RGB通道
    """
    img_height, img_width = image.shape[:2]

    # 将图像缩放到模型的输入尺寸中较短的一边
    min_scale = min(onnx_input_height / img_height, onnx_input_width / img_width)

    # 未进行padding之前的尺寸
    scale_height = int(round(img_height * min_scale))
    scale_width = int(round(img_width * min_scale))

    # 缩放到目标尺寸
    if onnx_input_height != img_height or onnx_input_width != img_width:  # 需要缩放
        input_img = np.full(shape=(onnx_input_height, onnx_input_width, 3),
                            fill_value=114, dtype=np.uint8)
        scale_img = cv2.resize(image, (scale_width, scale_height), interpolation=cv2.INTER_LINEAR)
        input_img[0:scale_height, 0:scale_width, :] = scale_img
    else:
        input_img = image

    # 缩放后最后的处理
    input_img = input_img / 255.0
    input_img = input_img.transpose(2, 0, 1)
    input_tensor = input_img[np.newaxis, :, :, :].astype(np.float32)

    return input_tensor, scale_height, scale_width

def nms(boxes, scores, iou_threshold):
    # Sort by score
    sorted_indices = np.argsort(scores)[::-1]

    keep_boxes = []
    while sorted_indices.size > 0:
        # Pick the last box
        box_id = sorted_indices[0]
        keep_boxes.append(box_id)

        # Compute IoU of the picked box with the rest
        ious = compute_iou(boxes[box_id, :], boxes[sorted_indices[1:], :])

        # Remove boxes with IoU over the threshold
        keep_indices = np.where(ious < iou_threshold)[0]

        # print(keep_indices.shape, sorted_indices.shape)
        sorted_indices = sorted_indices[keep_indices + 1]

    return keep_boxes


def multiclass_nms(boxes, scores, class_ids, iou_threshold):

    unique_class_ids = np.unique(class_ids)

    keep_boxes = []
    for class_id in unique_class_ids:
        class_indices = np.where(class_ids == class_id)[0]
        class_boxes = boxes[class_indices,:]
        class_scores = scores[class_indices]

        class_keep_boxes = nms(class_boxes, class_scores, iou_threshold)
        keep_boxes.extend(class_indices[class_keep_boxes])

    return keep_boxes


def compute_iou(box, boxes):
    # Compute xmin, ymin, xmax, ymax for both boxes
    xmin = np.maximum(box[0], boxes[:, 0])
    ymin = np.maximum(box[1], boxes[:, 1])
    xmax = np.minimum(box[2], boxes[:, 2])
    ymax = np.minimum(box[3], boxes[:, 3])

    # Compute intersection area
    intersection_area = np.maximum(0, xmax - xmin) * np.maximum(0, ymax - ymin)

    # Compute union area
    box_area = (box[2] - box[0]) * (box[3] - box[1])
    boxes_area = (boxes[:, 2] - boxes[:, 0]) * (boxes[:, 3] - boxes[:, 1])
    union_area = box_area + boxes_area - intersection_area

    # Compute IoU
    iou = intersection_area / union_area

    return iou


def xywh2xyxy(x):
    # Convert bounding box (x, y, w, h) to bounding box (x1, y1, x2, y2)
    y = np.copy(x)
    y[..., 0] = x[..., 0] - x[..., 2] / 2
    y[..., 1] = x[..., 1] - x[..., 3] / 2
    y[..., 2] = x[..., 0] + x[..., 2] / 2
    y[..., 3] = x[..., 1] + x[..., 3] / 2
    return y


def draw_detections(image: MatLike, result_list: list[YoloMatchResult], mask_alpha=0.3) -> MatLike:
    """
    在原图上绘制识别结果 返回一张新的图片
    :param result_list:
    :param mask_alpha:
    :return:
    """
    det_img = image.copy()

    img_height, img_width = image.shape[:2]
    font_size = min([img_height, img_width]) * 0.0006
    text_thickness = int(min([img_height, img_width]) * 0.001)

    det_img = draw_masks(det_img, result_list, mask_alpha)

    # Draw bounding boxes and labels of detections
    for result in result_list:
        color = get_color(result)

        cv2.rectangle(det_img, (result.x1, result.y1), (result.x2, result.y2), color, 2)

        caption = f'{result.data.class_name} {int(result.confidence * 100)}%'
        draw_text(det_img, caption, result, font_size, text_thickness)

    return det_img


def draw_text(image: np.ndarray, text: str, result: YoloMatchResult,
              font_size: float = 0.001, text_thickness: int = 2) -> np.ndarray:
    # opencv不支持中文 需要把中文字符置空
    import re
    text = re.sub(r'[\u4e00-\u9fa5]', '', text)
    rect = result.rect
    x1, y1, x2, y2 = rect.x1, rect.y1, rect.x2, rect.y2
    color = get_color(result)
    (tw, th), _ = cv2.getTextSize(text=text, fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                                  fontScale=font_size, thickness=text_thickness)
    th = int(th * 1.2)

    cv2.rectangle(image, (x1, y1),
                  (x1 + tw, y1 - th), color, -1)

    return cv2.putText(image, text, (x1, y1), cv2.FONT_HERSHEY_SIMPLEX, font_size, (255, 255, 255), text_thickness, cv2.LINE_AA)


def draw_masks(image: np.ndarray, results: List[YoloMatchResult], mask_alpha: float = 0.3) -> np.ndarray:
    mask_img = image.copy()

    # Draw bounding boxes and labels of detections
    for result in results:
        color = get_color(result)

        # Draw fill rectangle in mask image
        cv2.rectangle(mask_img,
                      (result.x1, result.y1),
                      (result.x2, result.y2), color, -1)

    return cv2.addWeighted(mask_img, mask_alpha, image, 1 - mask_alpha, 0)


def get_color(result: YoloMatchResult):
    return _COLORS[result.data.class_id]