import csv
import os
from typing import Optional, List

import numpy as np
from cv2.typing import <PERSON><PERSON><PERSON>

from op_purchase_ai.utils import os_utils
from op_purchase_ai.yolo import yolo_utils
from op_purchase_ai.yolo.yolo_match_result import Yo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, DetectContext, DetectClass
from op_purchase_ai.yolo.yolov8_onnx_base import Yolov8OnnxBase


class Yolov8Detector(Yolov8OnnxBase):

    def __init__(self,
                 model_name: str,
                 ):
        """
        yolov8 detect 导出 onnx 后使用
        参考自 https://github.com/ibaiGorordo/ONNX-YOLOv8-Object-Detection
        :param model_name: 模型名称 在根目录下会有一个以模型名称创建的子文件夹
        """
        Yolov8OnnxBase.__init__(
            self,
            model_name=model_name,
            model_parent_dir_path=os_utils.get_path_under_work_dir(['assets', 'models', 'yolo']),
        )

        self.idx_2_class: dict[int, DetectClass] = {}  # 分类
        self.class_2_idx: dict[str, int] = {}
        self.category_2_idx: dict[str, List[int]] = {}

    def check_model_exists(self) -> bool:
        if not Yolov8OnnxBase.check_model_exists(self):
            return False

        cls_csv_path = os.path.join(self.model_dir_path, 'labels.csv')
        if not os.path.exists(cls_csv_path):
            return False

        return True

    def init_model(self) -> None:
        Yolov8OnnxBase.init_model(self)
        self._load_detect_classes()

    def run(self, image: MatLike, conf: float = 0.6, iou: float = 0.5,
            run_time: Optional[float] = None,
            label_list: Optional[List[str]] = None,
            category_list: Optional[List[str]] = None) -> list[YoloMatchResult]:
        """
        对图片进行识别
        :param image: 使用 opencv 读取的图片 RGB通道
        :param conf: 置信度阈值
        :param iou: iou阈值
        :return: 识别结果
        """
        context = DetectContext(image, run_time)
        context.conf = conf
        context.iou = iou
        context.label_list = label_list
        context.category_list = category_list

        input_tensor = self.prepare_input(context)

        outputs = self.inference(input_tensor)

        return self.process_output(outputs, context)

    def prepare_input(self, context: DetectContext) -> np.ndarray:
        """
        推理前的预处理
        """
        input_tensor, scale_height, scale_width = yolo_utils.scale_input_image_u(context.img, self.onnx_input_width, self.onnx_input_height)
        context.scale_height = scale_height
        context.scale_width = scale_width
        return input_tensor

    def inference(self, input_tensor: np.ndarray):
        """
        图片输入到模型中进行推理
        :param input_tensor: 输入模型的图片 RGB通道
        :return: onnx模型推理得到的结果
        """
        outputs = self.session.run(self.output_names, {self.input_names[0]: input_tensor})
        return outputs

    def process_output(self, output, context: DetectContext) -> List[YoloMatchResult]:
        """
        :param output: 推理结果
        :param context: 上下文
        :return: 最终得到的识别结果
        """
        predictions = np.squeeze(output[0]).T

        keep = np.ones(shape=(predictions.shape[1]), dtype=bool)

        if context.label_list is not None or context.category_list is not None:
            keep[4:] = False  # 前4位是坐标 先把所有标签都设置为False
            if context.label_list is not None:
                for label in context.label_list:
                    idx = self.class_2_idx.get(label)
                    if idx is not None:
                        keep[idx + 4] = True

            if context.category_list is not None:
                for category in context.category_list:
                    for idx in self.category_2_idx.get(category, []):
                        keep[idx + 4] = True

        predictions[:, keep == False] = 0

        # 按置信度阈值进行基本的过滤
        scores = np.max(predictions[:, 4:], axis=1)
        predictions = predictions[scores > context.conf, :]
        scores = scores[scores > context.conf]

        results: List[YoloMatchResult] = []
        if len(scores) == 0:
            return results

        # 选择置信度最高的类别
        class_ids = np.argmax(predictions[:, 4:], axis=1)

        # 提取Bounding box
        boxes = predictions[:, :4]  # 原始推理结果 xywh
        scale_shape = np.array([context.scale_width, context.scale_height, context.scale_width, context.scale_height])  # 缩放后图片的大小
        boxes = np.divide(boxes, scale_shape, dtype=np.float32)  # 转化到 0~1
        boxes *= np.array([context.img_width, context.img_height, context.img_width, context.img_height])  # 恢复到原图的坐标
        boxes = yolo_utils.xywh2xyxy(boxes)  # 转化成 xyxy

        # 进行NMS 获取最后的结果
        indices = yolo_utils.multiclass_nms(boxes, scores, class_ids, context.iou)

        for idx in indices:
            rect = boxes[idx].tolist()
            x1: int = int(rect[0])
            y1: int = int(rect[1])
            x2: int = int(rect[2])
            y2: int = int(rect[3])
            yolo_result = YoloMatchResult(
                confidence=float(scores[idx]),
                x=x1,
                y=y1,
                w=x2 - x1,
                h=y2 - y1,
                data=self.idx_2_class[int(class_ids[idx])],
            )
            results.append(yolo_result)

        return results

    def _load_detect_classes(self):
        """
        加载分类
        :return:
        """
        csv_path = os.path.join(self.model_dir_path, 'labels.csv')
        if not os.path.exists(csv_path):
            raise FileNotFoundError(f'识别类别文件不存在')

        with open(csv_path, mode='r', encoding='utf-8') as file:
            csv_reader = csv.reader(file)
            for row in csv_reader:
                if row[0] == 'class_id':
                    continue
                c = DetectClass(class_id=int(row[0]), class_name=row[1], category=None if len(row) < 3 else row[2])
                self.idx_2_class[c.class_id] = c
                self.class_2_idx[c.class_name] = c.class_id

                if c.class_category not in self.category_2_idx:
                    self.category_2_idx[c.class_category] = []
                self.category_2_idx[c.class_category].append(c.class_id)


def __debug():
    model = Yolov8Detector(model_name='yolov8x-det')
    model.init_model()
    from op_purchase_ai.utils import cv2_utils
    img = cv2_utils.read_image(os.path.join(
        os_utils.get_path_under_work_dir(['.temp', 'test_files', 'longchamp', 'images']),
        '1.png'
    ))
    result_list = model.run(img)

    cv2_utils.show_image(yolo_utils.draw_detections(img, result_list),
                         max_height=1000, wait=0)


if __name__ == '__main__':
    __debug()