# TABLE类型商品信息提取实现方案

## 概述

TABLE类型（`AnalyseFileTaskType.TABLE = "EXCEL表格类"`）是系统中专门用于处理表格类文档的商品信息提取方案。该方案通过多种AI技术的组合，实现对表格类文档中商品信息的自动识别和提取。

## 核心架构

### 1. 处理流程概览

```mermaid
graph TD
    A[PDF文档] --> B[PDF转图片]
    B --> C[OCR文本识别]
    B --> D[YOLO图像识别]
    A --> E[PDF文本提取]
    A --> F[PDF图片提取]
    C --> G[结果过滤合并]
    D --> G
    E --> G
    F --> G
    G --> H[行合并处理]
    H --> I[表头识别]
    I --> J[属性映射]
    J --> K[商品信息提取]
    K --> L[最终结果]
```

## 详细实现流程

### 第一阶段：多源数据获取

#### 1.1 OCR文本识别
```python
ocr_result_list = app_context.ocr_service.ocr(page_image)
```
- 使用ONNX优化的OCR模型
- 支持多模型并行处理
- 返回文本内容及其位置信息

#### 1.2 YOLO图像识别
```python
yolo_result_list = app_context.yolo_service.run(page_image)
```
- 使用YOLOv8模型进行目标检测
- 识别图像中的商品图片区域
- 支持多类别检测

#### 1.3 PDF原生内容提取
```python
text_result_list, image_result_list = app_context.pdf_service.extract_word_and_image(
    pdf_file_path, request.page_num, page_image
)
```
- 直接从PDF文档提取文本和图片信息
- 保持原始坐标精度
- 处理缩放比例适配

### 第二阶段：结果过滤与合并

#### 2.1 重复内容过滤
```python
filter_ocr_result_list, filter_yolo_result_list = app_context.analyse_goods_service.filter_detect_result(
    ocr_result_list, yolo_result_list, text_result_list, image_result_list
)
```
- 比较模型识别结果与PDF提取结果
- 过滤重叠度超过80%的重复内容
- 保留精度更高的PDF原生结果

#### 2.2 统一数据格式
```python
detect_result_list: list[AnalyseFileDetectResult] = (
    detect_result_utils.list_from_text(filter_ocr_result_list, 'AUTO_TEXT_OCR')
    + detect_result_utils.list_from_text(text_result_list, 'AUTO_TEXT_PDF')
    + detect_result_utils.list_from_image(filter_yolo_result_list, 'AUTO_IMAGE_YOLO')
    + detect_result_utils.list_from_image(image_result_list, 'AUTO_IMAGE_PDF')
)
```

### 第三阶段：表格结构分析

#### 3.1 行合并处理
```python
line_list = detect_result_utils.merge_to_lines(detect_result_list)
```
- 按Y坐标将检测结果分组为行
- 行内元素按X坐标排序
- 使用高度的50%作为行判断阈值

#### 3.2 表头识别
```python
title_line = detect_result_utils.find_title_line(line_list, prop_list)
```
- 匹配属性示例与表头文本
- 选择匹配度最高的行作为表头
- 支持无表头情况的自动补充

#### 3.3 属性映射
```python
prop_idx_2_title_idx: dict[int, int] = detect_result_utils.get_prop_idx_2_title_idx(title_line, prop_list)
```
- 建立属性示例与表头列的对应关系
- 优先使用坐标匹配（左对齐原则）
- 备选文本相似度匹配

### 第四阶段：商品信息提取

#### 4.1 数据行处理
```python
for line in line_list:
    if line_rect.center.y < title_rect.y2:  # 过滤表头行上方的
        continue
```
- 筛选表头下方的数据行
- 验证行区域的有效性

#### 4.2 属性值提取
- 根据表头列位置提取对应属性值
- 处理文本和图像两种类型的属性
- 生成属性的位置坐标信息

#### 4.3 图像预测增强
```python
predict_image_list: list[YoloMatchResult] = app_context.analyse_goods_service.predict_image(
    task_type=request.task_type,
    page_image=page_image,
    prop_list=request.prop_list,
    detect_result_list=detect_result_list,
)
```
- 针对表格类型进行图像区域预测
- 基于属性示例坐标自动计算图像区域
- 补充遗漏的商品图片信息

## 关键算法

### 1. 行合并算法
```python
def merge_to_lines(detect_result_list: list[AnalyseFileDetectResult]) -> list[list[AnalyseFileDetectResult]]:
    # 按Y坐标排序
    sorted_det_result_list.sort(key=lambda x: x.rect.y1)
    
    # 分行逻辑：中心点Y坐标差值 > min(高度) * 0.5
    if abs(last_line_rect.center.y - det_rect.center.y) > min(last_line_rect.height, det_rect.height) * 0.5:
        # 新行
    else:
        # 同行
```

### 2. 表头匹配算法
```python
def find_title_line(line_list, prop_list):
    for line in line_list:
        match_cnt = 0
        for prop in prop_list:
            # 使用difflib进行文本相似度匹配
            prop_idx = str_utils.find_best_match_by_difflib(prop.prop_name, line_text_list)
            if prop_idx is not None:
                match_cnt += 1
    # 选择匹配数量最多的行
```

### 3. 属性映射算法
```python
def get_prop_idx_2_title_idx(title_line, prop_list):
    # 优先坐标匹配（左对齐）
    if prop.is_rect_valid:
        old_dis = abs(prop.rect.x1 - old_title.rect.x1)
        new_dis = abs(prop.rect.x1 - title.rect.x1)
        if new_dis < old_dis:
            # 更新映射
    # 备选文本匹配
    else:
        title_idx = str_utils.find_best_match_by_difflib(prop.prop_name, title_text_list)
```